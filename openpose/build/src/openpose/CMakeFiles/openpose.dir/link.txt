/usr/bin/g++-9 -fPIC  -fopenmp  -O3 -shared -Wl,-soname,libopenpose.so.1.7.0 -o libopenpose.so.1.7.0 CMakeFiles/openpose.dir/3d/cameraParameterReader.cpp.o CMakeFiles/openpose.dir/3d/defineTemplates.cpp.o CMakeFiles/openpose.dir/3d/jointAngleEstimation.cpp.o CMakeFiles/openpose.dir/3d/poseTriangulation.cpp.o CMakeFiles/openpose.dir/3d/poseTriangulationPrivate.cpp.o CMakeFiles/openpose.dir/calibration/cameraParameterEstimation.cpp.o CMakeFiles/openpose.dir/calibration/gridPatternFunctions.cpp.o CMakeFiles/openpose.dir/core/array.cpp.o CMakeFiles/openpose.dir/core/arrayCpuGpu.cpp.o CMakeFiles/openpose.dir/core/cvMatToOpInput.cpp.o CMakeFiles/openpose.dir/core/cvMatToOpOutput.cpp.o CMakeFiles/openpose.dir/core/datum.cpp.o CMakeFiles/openpose.dir/core/defineTemplates.cpp.o CMakeFiles/openpose.dir/core/gpuRenderer.cpp.o CMakeFiles/openpose.dir/core/keepTopNPeople.cpp.o CMakeFiles/openpose.dir/core/keypointScaler.cpp.o CMakeFiles/openpose.dir/core/matrix.cpp.o CMakeFiles/openpose.dir/core/opOutputToCvMat.cpp.o CMakeFiles/openpose.dir/core/point.cpp.o CMakeFiles/openpose.dir/core/rectangle.cpp.o CMakeFiles/openpose.dir/core/renderer.cpp.o CMakeFiles/openpose.dir/core/scaleAndSizeExtractor.cpp.o CMakeFiles/openpose.dir/core/string.cpp.o CMakeFiles/openpose.dir/core/verbosePrinter.cpp.o CMakeFiles/openpose.dir/face/defineTemplates.cpp.o CMakeFiles/openpose.dir/face/faceDetector.cpp.o CMakeFiles/openpose.dir/face/faceDetectorOpenCV.cpp.o CMakeFiles/openpose.dir/face/faceExtractorCaffe.cpp.o CMakeFiles/openpose.dir/face/faceExtractorNet.cpp.o CMakeFiles/openpose.dir/face/faceCpuRenderer.cpp.o CMakeFiles/openpose.dir/face/faceGpuRenderer.cpp.o CMakeFiles/openpose.dir/face/faceRenderer.cpp.o CMakeFiles/openpose.dir/face/renderFace.cpp.o CMakeFiles/openpose.dir/filestream/bvhSaver.cpp.o CMakeFiles/openpose.dir/filestream/cocoJsonSaver.cpp.o CMakeFiles/openpose.dir/filestream/defineTemplates.cpp.o CMakeFiles/openpose.dir/filestream/fileSaver.cpp.o CMakeFiles/openpose.dir/filestream/fileStream.cpp.o CMakeFiles/openpose.dir/filestream/heatMapSaver.cpp.o CMakeFiles/openpose.dir/filestream/imageSaver.cpp.o CMakeFiles/openpose.dir/filestream/jsonOfstream.cpp.o CMakeFiles/openpose.dir/filestream/keypointSaver.cpp.o CMakeFiles/openpose.dir/filestream/peopleJsonSaver.cpp.o CMakeFiles/openpose.dir/filestream/udpSender.cpp.o CMakeFiles/openpose.dir/filestream/videoSaver.cpp.o CMakeFiles/openpose.dir/gpu/cuda.cpp.o CMakeFiles/openpose.dir/gpu/gpu.cpp.o CMakeFiles/openpose.dir/gpu/opencl.cpp.o CMakeFiles/openpose.dir/gui/defineTemplates.cpp.o CMakeFiles/openpose.dir/gui/frameDisplayer.cpp.o CMakeFiles/openpose.dir/gui/gui.cpp.o CMakeFiles/openpose.dir/gui/guiAdam.cpp.o CMakeFiles/openpose.dir/gui/gui3D.cpp.o CMakeFiles/openpose.dir/gui/guiInfoAdder.cpp.o CMakeFiles/openpose.dir/hand/defineTemplates.cpp.o CMakeFiles/openpose.dir/hand/handDetector.cpp.o CMakeFiles/openpose.dir/hand/handDetectorFromTxt.cpp.o CMakeFiles/openpose.dir/hand/handExtractorCaffe.cpp.o CMakeFiles/openpose.dir/hand/handExtractorNet.cpp.o CMakeFiles/openpose.dir/hand/handCpuRenderer.cpp.o CMakeFiles/openpose.dir/hand/handGpuRenderer.cpp.o CMakeFiles/openpose.dir/hand/handRenderer.cpp.o CMakeFiles/openpose.dir/hand/renderHand.cpp.o CMakeFiles/openpose.dir/net/bodyPartConnectorBase.cpp.o CMakeFiles/openpose.dir/net/bodyPartConnectorBaseCL.cpp.o CMakeFiles/openpose.dir/net/bodyPartConnectorCaffe.cpp.o CMakeFiles/openpose.dir/net/maximumBase.cpp.o CMakeFiles/openpose.dir/net/maximumCaffe.cpp.o CMakeFiles/openpose.dir/net/netCaffe.cpp.o CMakeFiles/openpose.dir/net/netOpenCv.cpp.o CMakeFiles/openpose.dir/net/nmsBase.cpp.o CMakeFiles/openpose.dir/net/nmsBaseCL.cpp.o CMakeFiles/openpose.dir/net/nmsCaffe.cpp.o CMakeFiles/openpose.dir/net/resizeAndMergeBase.cpp.o CMakeFiles/openpose.dir/net/resizeAndMergeBaseCL.cpp.o CMakeFiles/openpose.dir/net/resizeAndMergeCaffe.cpp.o CMakeFiles/openpose.dir/pose/defineTemplates.cpp.o CMakeFiles/openpose.dir/pose/poseCpuRenderer.cpp.o CMakeFiles/openpose.dir/pose/poseExtractor.cpp.o CMakeFiles/openpose.dir/pose/poseExtractorCaffe.cpp.o CMakeFiles/openpose.dir/pose/poseExtractorNet.cpp.o CMakeFiles/openpose.dir/pose/poseGpuRenderer.cpp.o CMakeFiles/openpose.dir/pose/poseParameters.cpp.o CMakeFiles/openpose.dir/pose/poseParametersRender.cpp.o CMakeFiles/openpose.dir/pose/poseRenderer.cpp.o CMakeFiles/openpose.dir/pose/renderPose.cpp.o CMakeFiles/openpose.dir/producer/datumProducer.cpp.o CMakeFiles/openpose.dir/producer/defineTemplates.cpp.o CMakeFiles/openpose.dir/producer/flirReader.cpp.o CMakeFiles/openpose.dir/producer/imageDirectoryReader.cpp.o CMakeFiles/openpose.dir/producer/ipCameraReader.cpp.o CMakeFiles/openpose.dir/producer/producer.cpp.o CMakeFiles/openpose.dir/producer/spinnakerWrapper.cpp.o CMakeFiles/openpose.dir/producer/videoCaptureReader.cpp.o CMakeFiles/openpose.dir/producer/videoReader.cpp.o CMakeFiles/openpose.dir/producer/webcamReader.cpp.o CMakeFiles/openpose.dir/thread/defineTemplates.cpp.o CMakeFiles/openpose.dir/tracking/defineTemplates.cpp.o CMakeFiles/openpose.dir/tracking/personIdExtractor.cpp.o CMakeFiles/openpose.dir/tracking/personTracker.cpp.o CMakeFiles/openpose.dir/tracking/pyramidalLK.cpp.o CMakeFiles/openpose.dir/unity/unityBinding.cpp.o CMakeFiles/openpose.dir/utilities/errorAndLog.cpp.o CMakeFiles/openpose.dir/utilities/fileSystem.cpp.o CMakeFiles/openpose.dir/utilities/flagsToOpenPose.cpp.o CMakeFiles/openpose.dir/utilities/keypoint.cpp.o CMakeFiles/openpose.dir/utilities/openCv.cpp.o CMakeFiles/openpose.dir/utilities/openCvPrivate.cpp.o CMakeFiles/openpose.dir/utilities/profiler.cpp.o CMakeFiles/openpose.dir/utilities/string.cpp.o CMakeFiles/openpose.dir/wrapper/defineTemplates.cpp.o CMakeFiles/openpose.dir/wrapper/wrapperAuxiliary.cpp.o CMakeFiles/openpose.dir/wrapper/wrapperStructExtra.cpp.o CMakeFiles/openpose.dir/wrapper/wrapperStructFace.cpp.o CMakeFiles/openpose.dir/wrapper/wrapperStructGui.cpp.o CMakeFiles/openpose.dir/wrapper/wrapperStructHand.cpp.o CMakeFiles/openpose.dir/wrapper/wrapperStructInput.cpp.o CMakeFiles/openpose.dir/wrapper/wrapperStructOutput.cpp.o CMakeFiles/openpose.dir/wrapper/wrapperStructPose.cpp.o CMakeFiles/openpose.dir/face/openpose_generated_renderFace.cu.o CMakeFiles/openpose.dir/gpu/openpose_generated_cuda.cu.o CMakeFiles/openpose.dir/hand/openpose_generated_renderHand.cu.o CMakeFiles/openpose.dir/net/openpose_generated_bodyPartConnectorBase.cu.o CMakeFiles/openpose.dir/net/openpose_generated_maximumBase.cu.o CMakeFiles/openpose.dir/net/openpose_generated_nmsBase.cu.o CMakeFiles/openpose.dir/net/openpose_generated_resizeAndMergeBase.cu.o CMakeFiles/openpose.dir/pose/openpose_generated_renderPose.cu.o CMakeFiles/openpose.dir/tracking/openpose_generated_pyramidalLK.cu.o  -Wl,-rpath,/home/<USER>/eigenPose/openpose/build/caffe/lib: -Wl,-Bstatic -lcudart_static -Wl,-Bdynamic -ldl -Wl,-Bstatic -lrt -Wl,-Bdynamic /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_alphamat.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_barcode.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_intensity_transform.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_mcc.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_rapid.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_wechat_qrcode.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.5.4d -lglog ../../caffe/lib/libcaffe.so -lpthread ../../caffe/lib/libcaffe.so /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.5.4d /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.5.4d 
