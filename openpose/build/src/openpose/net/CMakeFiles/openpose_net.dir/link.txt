/usr/bin/g++-9 -fPIC  -fopenmp  -O3 -shared -Wl,-soname,libopenpose_net.so -o libopenpose_net.so CMakeFiles/openpose_net.dir/bodyPartConnectorBase.cpp.o CMakeFiles/openpose_net.dir/bodyPartConnectorBaseCL.cpp.o CMakeFiles/openpose_net.dir/bodyPartConnectorCaffe.cpp.o CMakeFiles/openpose_net.dir/maximumBase.cpp.o CMakeFiles/openpose_net.dir/maximumCaffe.cpp.o CMakeFiles/openpose_net.dir/netCaffe.cpp.o CMakeFiles/openpose_net.dir/netOpenCv.cpp.o CMakeFiles/openpose_net.dir/nmsBase.cpp.o CMakeFiles/openpose_net.dir/nmsBaseCL.cpp.o CMakeFiles/openpose_net.dir/nmsCaffe.cpp.o CMakeFiles/openpose_net.dir/resizeAndMergeBase.cpp.o CMakeFiles/openpose_net.dir/resizeAndMergeBaseCL.cpp.o CMakeFiles/openpose_net.dir/resizeAndMergeCaffe.cpp.o CMakeFiles/openpose_net.dir/openpose_net_generated_bodyPartConnectorBase.cu.o CMakeFiles/openpose_net.dir/openpose_net_generated_maximumBase.cu.o CMakeFiles/openpose_net.dir/openpose_net_generated_nmsBase.cu.o CMakeFiles/openpose_net.dir/openpose_net_generated_resizeAndMergeBase.cu.o  -Wl,-rpath,/home/<USER>/eigenPose/openpose/build/caffe/lib:/home/<USER>/eigenPose/openpose/build/src/openpose/core: -Wl,-Bstatic -lcudart_static -Wl,-Bdynamic -ldl -Wl,-Bstatic -lrt -Wl,-Bdynamic ../../../caffe/lib/libcaffe.so ../core/libopenpose_core.so -Wl,-Bstatic -lcudart_static -Wl,-Bdynamic -ldl -Wl,-Bstatic -lrt -Wl,-Bdynamic 
