/usr/bin/g++-9 -fPIC  -fopenmp  -O3 -shared -Wl,-soname,libopenpose_gpu.so -o libopenpose_gpu.so CMakeFiles/openpose_gpu.dir/cuda.cpp.o CMakeFiles/openpose_gpu.dir/gpu.cpp.o CMakeFiles/openpose_gpu.dir/opencl.cpp.o CMakeFiles/openpose_gpu.dir/openpose_gpu_generated_cuda.cu.o  -Wl,-rpath,/home/<USER>/eigenPose/openpose/build/src/openpose/core: -Wl,-Bstatic -lcudart_static -Wl,-Bdynamic -ldl -Wl,-Bstatic -lrt -Wl,-Bdynamic ../core/libopenpose_core.so -Wl,-Bstatic -lcudart_static -Wl,-Bdynamic -ldl -Wl,-Bstatic -lrt -Wl,-Bdynamic 
