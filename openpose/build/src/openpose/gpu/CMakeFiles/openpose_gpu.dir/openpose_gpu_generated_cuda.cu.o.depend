# Generated by: make2cmake.cmake
SET(CUDA_NVCC_DEPEND
  "/home/<USER>/eigenPose/openpose/include/openpose/core/array.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/core/arrayCpuGpu.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/core/common.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/core/datum.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/core/macros.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/core/matrix.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/core/point.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/core/rectangle.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/core/string.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/gpu/cuda.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/utilities/enumClasses.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/utilities/errorAndLog.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose/utilities/profiler.hpp"
 "/home/<USER>/eigenPose/openpose/include/openpose_private/gpu/cuda.hu"
 "/home/<USER>/eigenPose/openpose/src/openpose/gpu/cuda.cu"
 "/usr/include/alloca.h"
 "/usr/include/asm-generic/errno-base.h"
 "/usr/include/asm-generic/errno.h"
 "/usr/include/assert.h"
 "/usr/include/builtin_types.h"
 "/usr/include/c++/9/array"
 "/usr/include/c++/9/backward/auto_ptr.h"
 "/usr/include/c++/9/backward/binders.h"
 "/usr/include/c++/9/bits/alloc_traits.h"
 "/usr/include/c++/9/bits/allocated_ptr.h"
 "/usr/include/c++/9/bits/allocator.h"
 "/usr/include/c++/9/bits/atomic_base.h"
 "/usr/include/c++/9/bits/atomic_lockfree_defines.h"
 "/usr/include/c++/9/bits/basic_ios.h"
 "/usr/include/c++/9/bits/basic_ios.tcc"
 "/usr/include/c++/9/bits/basic_string.h"
 "/usr/include/c++/9/bits/basic_string.tcc"
 "/usr/include/c++/9/bits/char_traits.h"
 "/usr/include/c++/9/bits/concept_check.h"
 "/usr/include/c++/9/bits/cpp_type_traits.h"
 "/usr/include/c++/9/bits/cxxabi_forced.h"
 "/usr/include/c++/9/bits/cxxabi_init_exception.h"
 "/usr/include/c++/9/bits/exception.h"
 "/usr/include/c++/9/bits/exception_defines.h"
 "/usr/include/c++/9/bits/exception_ptr.h"
 "/usr/include/c++/9/bits/functexcept.h"
 "/usr/include/c++/9/bits/functional_hash.h"
 "/usr/include/c++/9/bits/hash_bytes.h"
 "/usr/include/c++/9/bits/invoke.h"
 "/usr/include/c++/9/bits/ios_base.h"
 "/usr/include/c++/9/bits/istream.tcc"
 "/usr/include/c++/9/bits/locale_classes.h"
 "/usr/include/c++/9/bits/locale_classes.tcc"
 "/usr/include/c++/9/bits/locale_facets.h"
 "/usr/include/c++/9/bits/locale_facets.tcc"
 "/usr/include/c++/9/bits/localefwd.h"
 "/usr/include/c++/9/bits/memoryfwd.h"
 "/usr/include/c++/9/bits/move.h"
 "/usr/include/c++/9/bits/nested_exception.h"
 "/usr/include/c++/9/bits/ostream.tcc"
 "/usr/include/c++/9/bits/ostream_insert.h"
 "/usr/include/c++/9/bits/parse_numbers.h"
 "/usr/include/c++/9/bits/postypes.h"
 "/usr/include/c++/9/bits/predefined_ops.h"
 "/usr/include/c++/9/bits/ptr_traits.h"
 "/usr/include/c++/9/bits/range_access.h"
 "/usr/include/c++/9/bits/refwrap.h"
 "/usr/include/c++/9/bits/shared_ptr.h"
 "/usr/include/c++/9/bits/shared_ptr_atomic.h"
 "/usr/include/c++/9/bits/shared_ptr_base.h"
 "/usr/include/c++/9/bits/sstream.tcc"
 "/usr/include/c++/9/bits/std_abs.h"
 "/usr/include/c++/9/bits/stl_algobase.h"
 "/usr/include/c++/9/bits/stl_bvector.h"
 "/usr/include/c++/9/bits/stl_construct.h"
 "/usr/include/c++/9/bits/stl_function.h"
 "/usr/include/c++/9/bits/stl_iterator.h"
 "/usr/include/c++/9/bits/stl_iterator_base_funcs.h"
 "/usr/include/c++/9/bits/stl_iterator_base_types.h"
 "/usr/include/c++/9/bits/stl_pair.h"
 "/usr/include/c++/9/bits/stl_raw_storage_iter.h"
 "/usr/include/c++/9/bits/stl_relops.h"
 "/usr/include/c++/9/bits/stl_tempbuf.h"
 "/usr/include/c++/9/bits/stl_uninitialized.h"
 "/usr/include/c++/9/bits/stl_vector.h"
 "/usr/include/c++/9/bits/streambuf.tcc"
 "/usr/include/c++/9/bits/streambuf_iterator.h"
 "/usr/include/c++/9/bits/stringfwd.h"
 "/usr/include/c++/9/bits/unique_ptr.h"
 "/usr/include/c++/9/bits/uses_allocator.h"
 "/usr/include/c++/9/bits/vector.tcc"
 "/usr/include/c++/9/cctype"
 "/usr/include/c++/9/cerrno"
 "/usr/include/c++/9/chrono"
 "/usr/include/c++/9/clocale"
 "/usr/include/c++/9/cmath"
 "/usr/include/c++/9/cstdint"
 "/usr/include/c++/9/cstdio"
 "/usr/include/c++/9/cstdlib"
 "/usr/include/c++/9/ctime"
 "/usr/include/c++/9/cwchar"
 "/usr/include/c++/9/cwctype"
 "/usr/include/c++/9/debug/assertions.h"
 "/usr/include/c++/9/debug/debug.h"
 "/usr/include/c++/9/exception"
 "/usr/include/c++/9/ext/aligned_buffer.h"
 "/usr/include/c++/9/ext/alloc_traits.h"
 "/usr/include/c++/9/ext/atomicity.h"
 "/usr/include/c++/9/ext/concurrence.h"
 "/usr/include/c++/9/ext/new_allocator.h"
 "/usr/include/c++/9/ext/numeric_traits.h"
 "/usr/include/c++/9/ext/string_conversions.h"
 "/usr/include/c++/9/ext/type_traits.h"
 "/usr/include/c++/9/initializer_list"
 "/usr/include/c++/9/ios"
 "/usr/include/c++/9/iosfwd"
 "/usr/include/c++/9/istream"
 "/usr/include/c++/9/limits"
 "/usr/include/c++/9/math.h"
 "/usr/include/c++/9/memory"
 "/usr/include/c++/9/new"
 "/usr/include/c++/9/ostream"
 "/usr/include/c++/9/ratio"
 "/usr/include/c++/9/sstream"
 "/usr/include/c++/9/stdexcept"
 "/usr/include/c++/9/stdlib.h"
 "/usr/include/c++/9/streambuf"
 "/usr/include/c++/9/string"
 "/usr/include/c++/9/system_error"
 "/usr/include/c++/9/thread"
 "/usr/include/c++/9/tuple"
 "/usr/include/c++/9/type_traits"
 "/usr/include/c++/9/typeinfo"
 "/usr/include/c++/9/utility"
 "/usr/include/c++/9/vector"
 "/usr/include/channel_descriptor.h"
 "/usr/include/crt/common_functions.h"
 "/usr/include/crt/device_double_functions.h"
 "/usr/include/crt/device_double_functions.hpp"
 "/usr/include/crt/device_functions.h"
 "/usr/include/crt/device_functions.hpp"
 "/usr/include/crt/host_config.h"
 "/usr/include/crt/host_defines.h"
 "/usr/include/crt/math_functions.h"
 "/usr/include/crt/math_functions.hpp"
 "/usr/include/crt/sm_70_rt.h"
 "/usr/include/crt/sm_70_rt.hpp"
 "/usr/include/crt/sm_80_rt.h"
 "/usr/include/crt/sm_80_rt.hpp"
 "/usr/include/ctype.h"
 "/usr/include/cuda.h"
 "/usr/include/cuda_device_runtime_api.h"
 "/usr/include/cuda_runtime.h"
 "/usr/include/cuda_runtime_api.h"
 "/usr/include/cuda_surface_types.h"
 "/usr/include/cuda_texture_types.h"
 "/usr/include/device_atomic_functions.h"
 "/usr/include/device_atomic_functions.hpp"
 "/usr/include/device_launch_parameters.h"
 "/usr/include/device_types.h"
 "/usr/include/driver_functions.h"
 "/usr/include/driver_types.h"
 "/usr/include/endian.h"
 "/usr/include/errno.h"
 "/usr/include/features-time64.h"
 "/usr/include/features.h"
 "/usr/include/library_types.h"
 "/usr/include/limits.h"
 "/usr/include/linux/errno.h"
 "/usr/include/linux/limits.h"
 "/usr/include/locale.h"
 "/usr/include/math.h"
 "/usr/include/pthread.h"
 "/usr/include/sched.h"
 "/usr/include/sm_20_atomic_functions.h"
 "/usr/include/sm_20_atomic_functions.hpp"
 "/usr/include/sm_20_intrinsics.h"
 "/usr/include/sm_20_intrinsics.hpp"
 "/usr/include/sm_30_intrinsics.h"
 "/usr/include/sm_30_intrinsics.hpp"
 "/usr/include/sm_32_atomic_functions.h"
 "/usr/include/sm_32_atomic_functions.hpp"
 "/usr/include/sm_32_intrinsics.h"
 "/usr/include/sm_32_intrinsics.hpp"
 "/usr/include/sm_35_atomic_functions.h"
 "/usr/include/sm_35_intrinsics.h"
 "/usr/include/sm_60_atomic_functions.h"
 "/usr/include/sm_60_atomic_functions.hpp"
 "/usr/include/sm_61_intrinsics.h"
 "/usr/include/sm_61_intrinsics.hpp"
 "/usr/include/stdc-predef.h"
 "/usr/include/stdint.h"
 "/usr/include/stdio.h"
 "/usr/include/stdlib.h"
 "/usr/include/string.h"
 "/usr/include/strings.h"
 "/usr/include/surface_functions.h"
 "/usr/include/surface_indirect_functions.h"
 "/usr/include/surface_types.h"
 "/usr/include/texture_fetch_functions.h"
 "/usr/include/texture_indirect_functions.h"
 "/usr/include/texture_types.h"
 "/usr/include/time.h"
 "/usr/include/vector_functions.h"
 "/usr/include/vector_functions.hpp"
 "/usr/include/vector_types.h"
 "/usr/include/wchar.h"
 "/usr/include/wctype.h"
 "/usr/include/x86_64-linux-gnu/asm/errno.h"
 "/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h"
 "/usr/include/x86_64-linux-gnu/bits/byteswap.h"
 "/usr/include/x86_64-linux-gnu/bits/cpu-set.h"
 "/usr/include/x86_64-linux-gnu/bits/endian.h"
 "/usr/include/x86_64-linux-gnu/bits/endianness.h"
 "/usr/include/x86_64-linux-gnu/bits/errno.h"
 "/usr/include/x86_64-linux-gnu/bits/floatn-common.h"
 "/usr/include/x86_64-linux-gnu/bits/floatn.h"
 "/usr/include/x86_64-linux-gnu/bits/flt-eval-method.h"
 "/usr/include/x86_64-linux-gnu/bits/fp-fast.h"
 "/usr/include/x86_64-linux-gnu/bits/fp-logb.h"
 "/usr/include/x86_64-linux-gnu/bits/iscanonical.h"
 "/usr/include/x86_64-linux-gnu/bits/libc-header-start.h"
 "/usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h"
 "/usr/include/x86_64-linux-gnu/bits/local_lim.h"
 "/usr/include/x86_64-linux-gnu/bits/locale.h"
 "/usr/include/x86_64-linux-gnu/bits/long-double.h"
 "/usr/include/x86_64-linux-gnu/bits/math-vector.h"
 "/usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h"
 "/usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h"
 "/usr/include/x86_64-linux-gnu/bits/mathcalls.h"
 "/usr/include/x86_64-linux-gnu/bits/posix1_lim.h"
 "/usr/include/x86_64-linux-gnu/bits/posix2_lim.h"
 "/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h"
 "/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h"
 "/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h"
 "/usr/include/x86_64-linux-gnu/bits/sched.h"
 "/usr/include/x86_64-linux-gnu/bits/select.h"
 "/usr/include/x86_64-linux-gnu/bits/select2.h"
 "/usr/include/x86_64-linux-gnu/bits/setjmp.h"
 "/usr/include/x86_64-linux-gnu/bits/stdint-intn.h"
 "/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h"
 "/usr/include/x86_64-linux-gnu/bits/stdio.h"
 "/usr/include/x86_64-linux-gnu/bits/stdio2.h"
 "/usr/include/x86_64-linux-gnu/bits/stdio_lim.h"
 "/usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h"
 "/usr/include/x86_64-linux-gnu/bits/stdlib-float.h"
 "/usr/include/x86_64-linux-gnu/bits/stdlib.h"
 "/usr/include/x86_64-linux-gnu/bits/string_fortified.h"
 "/usr/include/x86_64-linux-gnu/bits/strings_fortified.h"
 "/usr/include/x86_64-linux-gnu/bits/struct_mutex.h"
 "/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h"
 "/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h"
 "/usr/include/x86_64-linux-gnu/bits/time.h"
 "/usr/include/x86_64-linux-gnu/bits/time64.h"
 "/usr/include/x86_64-linux-gnu/bits/timesize.h"
 "/usr/include/x86_64-linux-gnu/bits/timex.h"
 "/usr/include/x86_64-linux-gnu/bits/types.h"
 "/usr/include/x86_64-linux-gnu/bits/types/FILE.h"
 "/usr/include/x86_64-linux-gnu/bits/types/__FILE.h"
 "/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/clock_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/error_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/locale_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h"
 "/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h"
 "/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h"
 "/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h"
 "/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h"
 "/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h"
 "/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h"
 "/usr/include/x86_64-linux-gnu/bits/types/time_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/timer_t.h"
 "/usr/include/x86_64-linux-gnu/bits/types/wint_t.h"
 "/usr/include/x86_64-linux-gnu/bits/typesizes.h"
 "/usr/include/x86_64-linux-gnu/bits/uintn-identity.h"
 "/usr/include/x86_64-linux-gnu/bits/uio_lim.h"
 "/usr/include/x86_64-linux-gnu/bits/waitflags.h"
 "/usr/include/x86_64-linux-gnu/bits/waitstatus.h"
 "/usr/include/x86_64-linux-gnu/bits/wchar.h"
 "/usr/include/x86_64-linux-gnu/bits/wchar2.h"
 "/usr/include/x86_64-linux-gnu/bits/wctype-wchar.h"
 "/usr/include/x86_64-linux-gnu/bits/wordsize.h"
 "/usr/include/x86_64-linux-gnu/bits/xopen_lim.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/atomic_word.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/c++allocator.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/c++config.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/c++locale.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/cpu_defines.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/ctype_base.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/ctype_inline.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/error_constants.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/gthr-default.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/gthr.h"
 "/usr/include/x86_64-linux-gnu/c++/9/bits/os_defines.h"
 "/usr/include/x86_64-linux-gnu/gnu/stubs-64.h"
 "/usr/include/x86_64-linux-gnu/gnu/stubs.h"
 "/usr/include/x86_64-linux-gnu/sys/cdefs.h"
 "/usr/include/x86_64-linux-gnu/sys/select.h"
 "/usr/include/x86_64-linux-gnu/sys/types.h"
 "/usr/lib/gcc/x86_64-linux-gnu/9/include/limits.h"
 "/usr/lib/gcc/x86_64-linux-gnu/9/include/stdarg.h"
 "/usr/lib/gcc/x86_64-linux-gnu/9/include/stddef.h"
 "/usr/lib/gcc/x86_64-linux-gnu/9/include/stdint.h"
 "/usr/lib/gcc/x86_64-linux-gnu/9/include/syslimits.h"
)

