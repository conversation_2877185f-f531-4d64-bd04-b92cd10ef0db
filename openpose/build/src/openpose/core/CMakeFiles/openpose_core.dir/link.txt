/usr/bin/g++-9 -fPIC  -fopenmp  -O3 -shared -Wl,-soname,libopenpose_core.so -o libopenpose_core.so CMakeFiles/openpose_core.dir/array.cpp.o CMakeFiles/openpose_core.dir/arrayCpuGpu.cpp.o CMakeFiles/openpose_core.dir/cvMatToOpInput.cpp.o CMakeFiles/openpose_core.dir/cvMatToOpOutput.cpp.o CMakeFiles/openpose_core.dir/datum.cpp.o CMakeFiles/openpose_core.dir/defineTemplates.cpp.o CMakeFiles/openpose_core.dir/gpuRenderer.cpp.o CMakeFiles/openpose_core.dir/keepTopNPeople.cpp.o CMakeFiles/openpose_core.dir/keypointScaler.cpp.o CMakeFiles/openpose_core.dir/matrix.cpp.o CMakeFiles/openpose_core.dir/opOutputToCvMat.cpp.o CMakeFiles/openpose_core.dir/point.cpp.o CMakeFiles/openpose_core.dir/rectangle.cpp.o CMakeFiles/openpose_core.dir/renderer.cpp.o CMakeFiles/openpose_core.dir/scaleAndSizeExtractor.cpp.o CMakeFiles/openpose_core.dir/string.cpp.o CMakeFiles/openpose_core.dir/verbosePrinter.cpp.o  -Wl,-Bstatic -lcudart_static -Wl,-Bdynamic -ldl -Wl,-Bstatic -lrt -Wl,-Bdynamic 
