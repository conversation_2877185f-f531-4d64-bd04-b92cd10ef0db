/usr/bin/g++-9 -fPIC  -fopenmp  -O3 -shared -Wl,-soname,libopenpose_filestream.so -o libopenpose_filestream.so CMakeFiles/openpose_filestream.dir/bvhSaver.cpp.o CMakeFiles/openpose_filestream.dir/cocoJsonSaver.cpp.o CMakeFiles/openpose_filestream.dir/defineTemplates.cpp.o CMakeFiles/openpose_filestream.dir/fileSaver.cpp.o CMakeFiles/openpose_filestream.dir/fileStream.cpp.o CMakeFiles/openpose_filestream.dir/heatMapSaver.cpp.o CMakeFiles/openpose_filestream.dir/imageSaver.cpp.o CMakeFiles/openpose_filestream.dir/jsonOfstream.cpp.o CMakeFiles/openpose_filestream.dir/keypointSaver.cpp.o CMakeFiles/openpose_filestream.dir/peopleJsonSaver.cpp.o CMakeFiles/openpose_filestream.dir/udpSender.cpp.o CMakeFiles/openpose_filestream.dir/videoSaver.cpp.o  -Wl,-rpath,/home/<USER>/eigenPose/openpose/build/src/openpose/core: ../core/libopenpose_core.so -Wl,-Bstatic -lcudart_static -Wl,-Bdynamic -ldl -Wl,-Bstatic -lrt -Wl,-Bdynamic 
