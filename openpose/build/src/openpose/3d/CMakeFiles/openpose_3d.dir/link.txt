/usr/bin/g++-9 -fPIC  -fopenmp  -O3 -shared -Wl,-soname,libopenpose_3d.so -o libopenpose_3d.so CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.o CMakeFiles/openpose_3d.dir/defineTemplates.cpp.o CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.o CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.o CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.o  -Wl,-rpath,/home/<USER>/eigenPose/openpose/build/caffe/lib:/home/<USER>/eigenPose/openpose/build/src/openpose/core: -Wl,-Bstatic -lcudart_static -Wl,-Bdynamic -ldl -Wl,-Bstatic -lrt -Wl,-Bdynamic ../../../caffe/lib/libcaffe.so ../core/libopenpose_core.so -Wl,-B<PERSON> -lcudart_static -Wl,-Bdynamic -ldl -Wl,-B<PERSON> -lrt -Wl,-Bdynamic 
