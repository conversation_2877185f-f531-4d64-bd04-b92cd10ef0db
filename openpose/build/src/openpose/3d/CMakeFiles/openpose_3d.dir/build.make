# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/eigenPose/openpose

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/eigenPose/openpose/build

# Include any dependencies generated for this target.
include src/openpose/3d/CMakeFiles/openpose_3d.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/openpose/3d/CMakeFiles/openpose_3d.dir/compiler_depend.make

# Include the progress variables for this target.
include src/openpose/3d/CMakeFiles/openpose_3d.dir/progress.make

# Include the compile flags for this target's objects.
include src/openpose/3d/CMakeFiles/openpose_3d.dir/flags.make

src/openpose/3d/CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.o: src/openpose/3d/CMakeFiles/openpose_3d.dir/flags.make
src/openpose/3d/CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.o: ../src/openpose/3d/cameraParameterReader.cpp
src/openpose/3d/CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.o: src/openpose/3d/CMakeFiles/openpose_3d.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/eigenPose/openpose/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/openpose/3d/CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.o"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/openpose/3d/CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.o -MF CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.o.d -o CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.o -c /home/<USER>/eigenPose/openpose/src/openpose/3d/cameraParameterReader.cpp

src/openpose/3d/CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.i"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/eigenPose/openpose/src/openpose/3d/cameraParameterReader.cpp > CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.i

src/openpose/3d/CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.s"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/eigenPose/openpose/src/openpose/3d/cameraParameterReader.cpp -o CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.s

src/openpose/3d/CMakeFiles/openpose_3d.dir/defineTemplates.cpp.o: src/openpose/3d/CMakeFiles/openpose_3d.dir/flags.make
src/openpose/3d/CMakeFiles/openpose_3d.dir/defineTemplates.cpp.o: ../src/openpose/3d/defineTemplates.cpp
src/openpose/3d/CMakeFiles/openpose_3d.dir/defineTemplates.cpp.o: src/openpose/3d/CMakeFiles/openpose_3d.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/eigenPose/openpose/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/openpose/3d/CMakeFiles/openpose_3d.dir/defineTemplates.cpp.o"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/openpose/3d/CMakeFiles/openpose_3d.dir/defineTemplates.cpp.o -MF CMakeFiles/openpose_3d.dir/defineTemplates.cpp.o.d -o CMakeFiles/openpose_3d.dir/defineTemplates.cpp.o -c /home/<USER>/eigenPose/openpose/src/openpose/3d/defineTemplates.cpp

src/openpose/3d/CMakeFiles/openpose_3d.dir/defineTemplates.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/openpose_3d.dir/defineTemplates.cpp.i"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/eigenPose/openpose/src/openpose/3d/defineTemplates.cpp > CMakeFiles/openpose_3d.dir/defineTemplates.cpp.i

src/openpose/3d/CMakeFiles/openpose_3d.dir/defineTemplates.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/openpose_3d.dir/defineTemplates.cpp.s"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/eigenPose/openpose/src/openpose/3d/defineTemplates.cpp -o CMakeFiles/openpose_3d.dir/defineTemplates.cpp.s

src/openpose/3d/CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.o: src/openpose/3d/CMakeFiles/openpose_3d.dir/flags.make
src/openpose/3d/CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.o: ../src/openpose/3d/jointAngleEstimation.cpp
src/openpose/3d/CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.o: src/openpose/3d/CMakeFiles/openpose_3d.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/eigenPose/openpose/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/openpose/3d/CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.o"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/openpose/3d/CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.o -MF CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.o.d -o CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.o -c /home/<USER>/eigenPose/openpose/src/openpose/3d/jointAngleEstimation.cpp

src/openpose/3d/CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.i"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/eigenPose/openpose/src/openpose/3d/jointAngleEstimation.cpp > CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.i

src/openpose/3d/CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.s"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/eigenPose/openpose/src/openpose/3d/jointAngleEstimation.cpp -o CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.s

src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.o: src/openpose/3d/CMakeFiles/openpose_3d.dir/flags.make
src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.o: ../src/openpose/3d/poseTriangulation.cpp
src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.o: src/openpose/3d/CMakeFiles/openpose_3d.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/eigenPose/openpose/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.o"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.o -MF CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.o.d -o CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.o -c /home/<USER>/eigenPose/openpose/src/openpose/3d/poseTriangulation.cpp

src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.i"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/eigenPose/openpose/src/openpose/3d/poseTriangulation.cpp > CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.i

src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.s"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/eigenPose/openpose/src/openpose/3d/poseTriangulation.cpp -o CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.s

src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.o: src/openpose/3d/CMakeFiles/openpose_3d.dir/flags.make
src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.o: ../src/openpose/3d/poseTriangulationPrivate.cpp
src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.o: src/openpose/3d/CMakeFiles/openpose_3d.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/eigenPose/openpose/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.o"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.o -MF CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.o.d -o CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.o -c /home/<USER>/eigenPose/openpose/src/openpose/3d/poseTriangulationPrivate.cpp

src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.i"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/eigenPose/openpose/src/openpose/3d/poseTriangulationPrivate.cpp > CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.i

src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.s"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && /usr/bin/g++-9 $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/eigenPose/openpose/src/openpose/3d/poseTriangulationPrivate.cpp -o CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.s

# Object files for target openpose_3d
openpose_3d_OBJECTS = \
"CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.o" \
"CMakeFiles/openpose_3d.dir/defineTemplates.cpp.o" \
"CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.o" \
"CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.o" \
"CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.o"

# External object files for target openpose_3d
openpose_3d_EXTERNAL_OBJECTS =

src/openpose/3d/libopenpose_3d.so: src/openpose/3d/CMakeFiles/openpose_3d.dir/cameraParameterReader.cpp.o
src/openpose/3d/libopenpose_3d.so: src/openpose/3d/CMakeFiles/openpose_3d.dir/defineTemplates.cpp.o
src/openpose/3d/libopenpose_3d.so: src/openpose/3d/CMakeFiles/openpose_3d.dir/jointAngleEstimation.cpp.o
src/openpose/3d/libopenpose_3d.so: src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulation.cpp.o
src/openpose/3d/libopenpose_3d.so: src/openpose/3d/CMakeFiles/openpose_3d.dir/poseTriangulationPrivate.cpp.o
src/openpose/3d/libopenpose_3d.so: src/openpose/3d/CMakeFiles/openpose_3d.dir/build.make
src/openpose/3d/libopenpose_3d.so: /usr/lib/x86_64-linux-gnu/libcudart_static.a
src/openpose/3d/libopenpose_3d.so: /usr/lib/x86_64-linux-gnu/librt.a
src/openpose/3d/libopenpose_3d.so: caffe/lib/libcaffe.so
src/openpose/3d/libopenpose_3d.so: src/openpose/core/libopenpose_core.so
src/openpose/3d/libopenpose_3d.so: /usr/lib/x86_64-linux-gnu/libcudart_static.a
src/openpose/3d/libopenpose_3d.so: /usr/lib/x86_64-linux-gnu/librt.a
src/openpose/3d/libopenpose_3d.so: src/openpose/3d/CMakeFiles/openpose_3d.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/eigenPose/openpose/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX shared library libopenpose_3d.so"
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/openpose_3d.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/openpose/3d/CMakeFiles/openpose_3d.dir/build: src/openpose/3d/libopenpose_3d.so
.PHONY : src/openpose/3d/CMakeFiles/openpose_3d.dir/build

src/openpose/3d/CMakeFiles/openpose_3d.dir/clean:
	cd /home/<USER>/eigenPose/openpose/build/src/openpose/3d && $(CMAKE_COMMAND) -P CMakeFiles/openpose_3d.dir/cmake_clean.cmake
.PHONY : src/openpose/3d/CMakeFiles/openpose_3d.dir/clean

src/openpose/3d/CMakeFiles/openpose_3d.dir/depend:
	cd /home/<USER>/eigenPose/openpose/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/eigenPose/openpose /home/<USER>/eigenPose/openpose/src/openpose/3d /home/<USER>/eigenPose/openpose/build /home/<USER>/eigenPose/openpose/build/src/openpose/3d /home/<USER>/eigenPose/openpose/build/src/openpose/3d/CMakeFiles/openpose_3d.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/openpose/3d/CMakeFiles/openpose_3d.dir/depend

