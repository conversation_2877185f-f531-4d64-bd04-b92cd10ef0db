# This is the CMakeCache file.
# For build in directory: /home/<USER>/eigenPose/openpose/build
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build Caffe as part of OpenPose.
BUILD_CAFFE:BOOL=ON

//Build OpenPose documentation.
BUILD_DOCS:BOOL=OFF

//Build OpenPose examples.
BUILD_EXAMPLES:BOOL=ON

//Build OpenPose python.
BUILD_PYTHON:BOOL=ON

//Build as shared lib.
BUILD_SHARED_LIBS:BOOL=ON

//Build OpenPose as a Unity plugin.
BUILD_UNITY_SUPPORT:BOOL=OFF

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build.
CMAKE_BUILD_TYPE:STRING=Release

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/g++-9

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/gcc-9

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=OpenPose

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.7.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=7

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Compile device code in 64 bit mode
CUDA_64_BIT_DEVICE_CODE:BOOL=ON

//Select target NVIDIA GPU architecture.
CUDA_ARCH:STRING=Auto

//Attach the build rule to the CUDA source file.  Enable only when
// the CUDA source file is added to at most one target.
CUDA_ATTACH_VS_BUILD_RULE_TO_CUDA_FILE:BOOL=ON

//Generate and parse .cubin files in Device mode.
CUDA_BUILD_CUBIN:BOOL=OFF

//Build in Emulation mode
CUDA_BUILD_EMULATION:BOOL=OFF

//"cudart" library
CUDA_CUDART_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcudart.so

//"cuda" library (older versions only).
CUDA_CUDA_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcuda.so

//Directory to put all the output files.  If blank it will default
// to the CMAKE_CURRENT_BINARY_DIR
CUDA_GENERATED_OUTPUT_DIR:PATH=

//Generated file extension
CUDA_HOST_COMPILATION_CPP:BOOL=ON

//Host side compiler used by NVCC
CUDA_HOST_COMPILER:FILEPATH=/usr/bin/g++-9

//Path to a program.
CUDA_NVCC_EXECUTABLE:FILEPATH=/usr/bin/nvcc

//Semi-colon delimit multiple arguments. during all build types.
CUDA_NVCC_FLAGS:STRING=

//Semi-colon delimit multiple arguments. during DEBUG builds.
CUDA_NVCC_FLAGS_DEBUG:STRING=

//Semi-colon delimit multiple arguments. during MINSIZEREL builds.
CUDA_NVCC_FLAGS_MINSIZEREL:STRING=

//Semi-colon delimit multiple arguments. during RELEASE builds.
CUDA_NVCC_FLAGS_RELEASE:STRING=

//Semi-colon delimit multiple arguments. during RELWITHDEBINFO
// builds.
CUDA_NVCC_FLAGS_RELWITHDEBINFO:STRING=

//"OpenCL" library
CUDA_OpenCL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libOpenCL.so

//Propagate C/CXX_FLAGS and friends to the host compiler via -Xcompile
CUDA_PROPAGATE_HOST_FLAGS:BOOL=ON

//Path to a file.
CUDA_SDK_ROOT_DIR:PATH=CUDA_SDK_ROOT_DIR-NOTFOUND

//Compile CUDA objects with separable compilation enabled.  Requires
// CUDA 5.0+
CUDA_SEPARABLE_COMPILATION:BOOL=OFF

//Path to a file.
CUDA_TOOLKIT_INCLUDE:PATH=/usr/include

//Toolkit location.
CUDA_TOOLKIT_ROOT_DIR:PATH=/usr

//Use the static version of the CUDA runtime library if available
CUDA_USE_STATIC_CUDA_RUNTIME:BOOL=ON

//Print out the commands run while compiling the CUDA source file.
//  With the Makefile generator this defaults to VERBOSE variable
// specified on the command line, but can be forced on with this
// option.
CUDA_VERBOSE_BUILD:BOOL=OFF

//Version of CUDA as computed from nvcc.
CUDA_VERSION:STRING=11.5

//"cublas" library
CUDA_cublas_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcublas.so

//"cudadevrt" library
CUDA_cudadevrt_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcudadevrt.a

//static CUDA runtime library
CUDA_cudart_static_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcudart_static.a

//"cufft" library
CUDA_cufft_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcufft.so

//"cupti" library
CUDA_cupti_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcupti.so

//"curand" library
CUDA_curand_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcurand.so

//"cusolver" library
CUDA_cusolver_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcusolver.so

//"cusparse" library
CUDA_cusparse_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcusparse.so

//"nppc" library
CUDA_nppc_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnppc.so

//"nppial" library
CUDA_nppial_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnppial.so

//"nppicc" library
CUDA_nppicc_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnppicc.so

//"nppidei" library
CUDA_nppidei_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnppidei.so

//"nppif" library
CUDA_nppif_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnppif.so

//"nppig" library
CUDA_nppig_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnppig.so

//"nppim" library
CUDA_nppim_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnppim.so

//"nppist" library
CUDA_nppist_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnppist.so

//"nppisu" library
CUDA_nppisu_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnppisu.so

//"nppitc" library
CUDA_nppitc_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnppitc.so

//"npps" library
CUDA_npps_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnpps.so

//"nvToolsExt" library
CUDA_nvToolsExt_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libnvToolsExt.so

//Path to a library.
CUDA_rt_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/librt.a

//Path to cuDNN include directory.
CUDNN_INCLUDE:PATH=/usr/include

//Path to cuDNN library.
CUDNN_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcudnn.so

//CUDNN root folder
CUDNN_ROOT:PATH=

//Path to a file.
Caffe_INCLUDE_DIRS:PATH=/home/<USER>/eigenPose/openpose/build/caffe/include

//Path to a library.
Caffe_LIBS:FILEPATH=/home/<USER>/eigenPose/openpose/build/caffe/lib/libcaffe.so

//Select Deep Learning Framework.
DL_FRAMEWORK:STRING=CAFFE

//Download body 25-keypoint (body COCO and 6-keypoint foot) model.
DOWNLOAD_BODY_25_MODEL:BOOL=ON

//Download body 18-keypoint COCO model.
DOWNLOAD_BODY_COCO_MODEL:BOOL=OFF

//Download body 15-keypoint MPI model.
DOWNLOAD_BODY_MPI_MODEL:BOOL=OFF

//Download face model.
DOWNLOAD_FACE_MODEL:BOOL=ON

//Download hand model.
DOWNLOAD_HAND_MODEL:BOOL=ON

//Server from which the models and 3rdparty libraries will be downloaded
// from.
DOWNLOAD_SERVER:STRING=http://vcl.snu.ac.kr/OpenPose/

//Path to a file.
GFLAGS_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
GFLAGS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgflags.so

//Folder contains Gflags
GFLAGS_ROOT_DIR:PATH=

//Path to a file.
GLOG_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
GLOG_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libglog.so

//Folder contains Google glog
GLOG_ROOT_DIR:PATH=

//Select the acceleration GPU library or CPU otherwise.
GPU_MODE:STRING=CUDA

//Enable Enhanced Instruction Set
INSTRUCTION_SET:STRING=NONE

//The directory containing a CMake configuration file for OpenCV.
OpenCV_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/opencv4

//Value Computed by CMake
OpenPose_BINARY_DIR:STATIC=/home/<USER>/eigenPose/openpose/build

//Value Computed by CMake
OpenPose_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
OpenPose_SOURCE_DIR:STATIC=/home/<USER>/eigenPose/openpose

//If enabled, OpenPose will be able to print out speed information
// at runtime.
PROFILER_ENABLED:BOOL=OFF

//C++ standard flag, e.g. -std=c++11, -std=c++14, /std:c++14. 
// Defaults to C++14 mode.
PYBIND11_CPP_STANDARD:STRING=-std=c++14

//Install pybind11 header files?
PYBIND11_INSTALL:BOOL=OFF

//Python version to use for compiling modules
PYBIND11_PYTHON_VERSION:STRING=

//Build pybind11 test suite?
PYBIND11_TEST:BOOL=OFF

//Path to a program.
PYTHON_EXECUTABLE:FILEPATH=/usr/bin/python3.10

//Path to a library.
PYTHON_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpython3.10.so

//Path to a file.
Protobuf_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
Protobuf_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libprotobuf.so

//Path to a library.
Protobuf_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libprotobuf.so

//Path to a library.
Protobuf_LITE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libprotobuf-lite.so

//Path to a library.
Protobuf_LITE_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libprotobuf-lite.so

//The Google Protocol Buffers Compiler
Protobuf_PROTOC_EXECUTABLE:FILEPATH=/usr/bin/protoc

//Path to a library.
Protobuf_PROTOC_LIBRARY_DEBUG:FILEPATH=Protobuf_PROTOC_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
Protobuf_PROTOC_LIBRARY_RELEASE:FILEPATH=Protobuf_PROTOC_LIBRARY_RELEASE-NOTFOUND

//Build OpenPose with cuDNN library support.
USE_CUDNN:BOOL=ON

//Install pybind11 headers in Python include directory instead
// of default installation prefix
USE_PYTHON_INCLUDE_DIR:BOOL=OFF

//Add OpenPose 3D renderer module (it requires FreeGLUT library).
WITH_3D_RENDERER:BOOL=OFF

//Add Ceres support for advanced 3-D reconstruction.
WITH_CERES:BOOL=OFF

//Select the Eigen mode: NONE if not required, AUTOBUILD to let
// OpenPose download it, or FIND to let CMake find it (e.g., if
// you installed it manually or used `sudo apt-get install libeigen3-dev`).
WITH_EIGEN:STRING=NONE

//Add FLIR (formerly Point Grey) camera code (requires Spinnaker
// SDK already installed).
WITH_FLIR_CAMERA:BOOL=OFF

//Much faster GUI display, but you must also enable OpenGL support
// in OpenCV by configuring OpenCV using CMake with WITH_OPENGL=ON
// flag.
WITH_OPENCV_WITH_OPENGL:BOOL=OFF

//Dependencies for the target
openpose_3d_LIB_DEPENDS:STATIC=general;/usr/lib/x86_64-linux-gnu/libcudart_static.a;general;dl;general;/usr/lib/x86_64-linux-gnu/librt.a;general;caffe;general;openpose_core;

//Dependencies for the target
openpose_LIB_DEPENDS:STATIC=general;/usr/lib/x86_64-linux-gnu/libcudart_static.a;general;dl;general;/usr/lib/x86_64-linux-gnu/librt.a;general;opencv_calib3d;general;opencv_core;general;opencv_dnn;general;opencv_features2d;general;opencv_flann;general;opencv_highgui;general;opencv_imgcodecs;general;opencv_imgproc;general;opencv_ml;general;opencv_objdetect;general;opencv_photo;general;opencv_stitching;general;opencv_video;general;opencv_videoio;general;opencv_alphamat;general;opencv_aruco;general;opencv_barcode;general;opencv_bgsegm;general;opencv_bioinspired;general;opencv_ccalib;general;opencv_datasets;general;opencv_dnn_objdetect;general;opencv_dnn_superres;general;opencv_dpm;general;opencv_face;general;opencv_freetype;general;opencv_fuzzy;general;opencv_hdf;general;opencv_hfs;general;opencv_img_hash;general;opencv_intensity_transform;general;opencv_line_descriptor;general;opencv_mcc;general;opencv_optflow;general;opencv_phase_unwrapping;general;opencv_plot;general;opencv_quality;general;opencv_rapid;general;opencv_reg;general;opencv_rgbd;general;opencv_saliency;general;opencv_shape;general;opencv_stereo;general;opencv_structured_light;general;opencv_superres;general;opencv_surface_matching;general;opencv_text;general;opencv_tracking;general;opencv_videostab;general;opencv_viz;general;opencv_wechat_qrcode;general;opencv_ximgproc;general;opencv_xobjdetect;general;opencv_xphoto;general;/usr/lib/x86_64-linux-gnu/libglog.so;general;/home/<USER>/eigenPose/openpose/build/caffe/lib/libcaffe.so;general;pthread;general;caffe;

//Dependencies for the target
openpose_calibration_LIB_DEPENDS:STATIC=general;/usr/lib/x86_64-linux-gnu/libcudart_static.a;general;dl;general;/usr/lib/x86_64-linux-gnu/librt.a;general;openpose_core;

//Dependencies for the target
openpose_core_LIB_DEPENDS:STATIC=general;/usr/lib/x86_64-linux-gnu/libcudart_static.a;general;dl;general;/usr/lib/x86_64-linux-gnu/librt.a;

//Dependencies for the target
openpose_face_LIB_DEPENDS:STATIC=general;/usr/lib/x86_64-linux-gnu/libcudart_static.a;general;dl;general;/usr/lib/x86_64-linux-gnu/librt.a;general;openpose_core;

//Dependencies for the target
openpose_filestream_LIB_DEPENDS:STATIC=general;openpose_core;

//Dependencies for the target
openpose_gpu_LIB_DEPENDS:STATIC=general;/usr/lib/x86_64-linux-gnu/libcudart_static.a;general;dl;general;/usr/lib/x86_64-linux-gnu/librt.a;general;openpose_core;

//Dependencies for the target
openpose_gui_LIB_DEPENDS:STATIC=general;openpose_pose;general;opencv_calib3d;general;opencv_core;general;opencv_dnn;general;opencv_features2d;general;opencv_flann;general;opencv_highgui;general;opencv_imgcodecs;general;opencv_imgproc;general;opencv_ml;general;opencv_objdetect;general;opencv_photo;general;opencv_stitching;general;opencv_video;general;opencv_videoio;general;opencv_alphamat;general;opencv_aruco;general;opencv_barcode;general;opencv_bgsegm;general;opencv_bioinspired;general;opencv_ccalib;general;opencv_datasets;general;opencv_dnn_objdetect;general;opencv_dnn_superres;general;opencv_dpm;general;opencv_face;general;opencv_freetype;general;opencv_fuzzy;general;opencv_hdf;general;opencv_hfs;general;opencv_img_hash;general;opencv_intensity_transform;general;opencv_line_descriptor;general;opencv_mcc;general;opencv_optflow;general;opencv_phase_unwrapping;general;opencv_plot;general;opencv_quality;general;opencv_rapid;general;opencv_reg;general;opencv_rgbd;general;opencv_saliency;general;opencv_shape;general;opencv_stereo;general;opencv_structured_light;general;opencv_superres;general;opencv_surface_matching;general;opencv_text;general;opencv_tracking;general;opencv_videostab;general;opencv_viz;general;opencv_wechat_qrcode;general;opencv_ximgproc;general;opencv_xobjdetect;general;opencv_xphoto;

//Dependencies for the target
openpose_hand_LIB_DEPENDS:STATIC=general;/usr/lib/x86_64-linux-gnu/libcudart_static.a;general;dl;general;/usr/lib/x86_64-linux-gnu/librt.a;general;openpose_core;

//Dependencies for the target
openpose_net_LIB_DEPENDS:STATIC=general;/usr/lib/x86_64-linux-gnu/libcudart_static.a;general;dl;general;/usr/lib/x86_64-linux-gnu/librt.a;general;caffe;general;openpose_core;

//Dependencies for the target
openpose_pose_LIB_DEPENDS:STATIC=general;/usr/lib/x86_64-linux-gnu/libcudart_static.a;general;dl;general;/usr/lib/x86_64-linux-gnu/librt.a;general;openpose_core;

//Dependencies for the target
openpose_producer_LIB_DEPENDS:STATIC=general;opencv_calib3d;general;opencv_core;general;opencv_dnn;general;opencv_features2d;general;opencv_flann;general;opencv_highgui;general;opencv_imgcodecs;general;opencv_imgproc;general;opencv_ml;general;opencv_objdetect;general;opencv_photo;general;opencv_stitching;general;opencv_video;general;opencv_videoio;general;opencv_alphamat;general;opencv_aruco;general;opencv_barcode;general;opencv_bgsegm;general;opencv_bioinspired;general;opencv_ccalib;general;opencv_datasets;general;opencv_dnn_objdetect;general;opencv_dnn_superres;general;opencv_dpm;general;opencv_face;general;opencv_freetype;general;opencv_fuzzy;general;opencv_hdf;general;opencv_hfs;general;opencv_img_hash;general;opencv_intensity_transform;general;opencv_line_descriptor;general;opencv_mcc;general;opencv_optflow;general;opencv_phase_unwrapping;general;opencv_plot;general;opencv_quality;general;opencv_rapid;general;opencv_reg;general;opencv_rgbd;general;opencv_saliency;general;opencv_shape;general;opencv_stereo;general;opencv_structured_light;general;opencv_superres;general;opencv_surface_matching;general;opencv_text;general;opencv_tracking;general;opencv_videostab;general;opencv_viz;general;opencv_wechat_qrcode;general;opencv_ximgproc;general;opencv_xobjdetect;general;opencv_xphoto;general;openpose_core;general;openpose_thread;general;openpose_filestream;

//Dependencies for the target
openpose_thread_LIB_DEPENDS:STATIC=general;openpose_core;

//Dependencies for the target
openpose_tracking_LIB_DEPENDS:STATIC=general;openpose_core;

//Dependencies for the target
openpose_unity_LIB_DEPENDS:STATIC=general;openpose_pose;general;opencv_calib3d;general;opencv_core;general;opencv_dnn;general;opencv_features2d;general;opencv_flann;general;opencv_highgui;general;opencv_imgcodecs;general;opencv_imgproc;general;opencv_ml;general;opencv_objdetect;general;opencv_photo;general;opencv_stitching;general;opencv_video;general;opencv_videoio;general;opencv_alphamat;general;opencv_aruco;general;opencv_barcode;general;opencv_bgsegm;general;opencv_bioinspired;general;opencv_ccalib;general;opencv_datasets;general;opencv_dnn_objdetect;general;opencv_dnn_superres;general;opencv_dpm;general;opencv_face;general;opencv_freetype;general;opencv_fuzzy;general;opencv_hdf;general;opencv_hfs;general;opencv_img_hash;general;opencv_intensity_transform;general;opencv_line_descriptor;general;opencv_mcc;general;opencv_optflow;general;opencv_phase_unwrapping;general;opencv_plot;general;opencv_quality;general;opencv_rapid;general;opencv_reg;general;opencv_rgbd;general;opencv_saliency;general;opencv_shape;general;opencv_stereo;general;opencv_structured_light;general;opencv_superres;general;opencv_surface_matching;general;opencv_text;general;opencv_tracking;general;opencv_videostab;general;opencv_viz;general;opencv_wechat_qrcode;general;opencv_ximgproc;general;opencv_xobjdetect;general;opencv_xphoto;general;/usr/lib/x86_64-linux-gnu/libglog.so;general;/home/<USER>/eigenPose/openpose/build/caffe/lib/libcaffe.so;general;pthread;

//Dependencies for the target
openpose_utilities_LIB_DEPENDS:STATIC=general;/usr/lib/x86_64-linux-gnu/libcudart_static.a;general;dl;general;/usr/lib/x86_64-linux-gnu/librt.a;general;openpose_producer;general;openpose_filestream;

//Dependencies for the target
openpose_wrapper_LIB_DEPENDS:STATIC=general;openpose_thread;general;openpose_pose;general;openpose_hand;general;openpose_core;general;openpose_face;general;openpose_filestream;general;openpose_gui;general;openpose_producer;general;openpose_utilities;

//Value Computed by CMake
pybind11_BINARY_DIR:STATIC=/home/<USER>/eigenPose/openpose/build/3rdparty/pybind11

//Value Computed by CMake
pybind11_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
pybind11_SOURCE_DIR:STATIC=/home/<USER>/eigenPose/openpose/3rdparty/pybind11

//Dependencies for the target
pyopenpose_LIB_DEPENDS:STATIC=general;openpose;general;opencv_calib3d;general;opencv_core;general;opencv_dnn;general;opencv_features2d;general;opencv_flann;general;opencv_highgui;general;opencv_imgcodecs;general;opencv_imgproc;general;opencv_ml;general;opencv_objdetect;general;opencv_photo;general;opencv_stitching;general;opencv_video;general;opencv_videoio;general;opencv_alphamat;general;opencv_aruco;general;opencv_barcode;general;opencv_bgsegm;general;opencv_bioinspired;general;opencv_ccalib;general;opencv_datasets;general;opencv_dnn_objdetect;general;opencv_dnn_superres;general;opencv_dpm;general;opencv_face;general;opencv_freetype;general;opencv_fuzzy;general;opencv_hdf;general;opencv_hfs;general;opencv_img_hash;general;opencv_intensity_transform;general;opencv_line_descriptor;general;opencv_mcc;general;opencv_optflow;general;opencv_phase_unwrapping;general;opencv_plot;general;opencv_quality;general;opencv_rapid;general;opencv_reg;general;opencv_rgbd;general;opencv_saliency;general;opencv_shape;general;opencv_stereo;general;opencv_structured_light;general;opencv_superres;general;opencv_surface_matching;general;opencv_text;general;opencv_tracking;general;opencv_videostab;general;opencv_viz;general;opencv_wechat_qrcode;general;opencv_ximgproc;general;opencv_xobjdetect;general;opencv_xphoto;general;/usr/lib/x86_64-linux-gnu/libglog.so;general;/home/<USER>/eigenPose/openpose/build/caffe/lib/libcaffe.so;general;pthread;


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//STRINGS property for variable: CMAKE_BUILD_TYPE
CMAKE_BUILD_TYPE-STRINGS:INTERNAL=Release;Debug;MinSizeRel;RelWithDebInfo
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/eigenPose/openpose/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=22
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/eigenPose/openpose
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=30
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.22
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_64_BIT_DEVICE_CODE
CUDA_64_BIT_DEVICE_CODE-ADVANCED:INTERNAL=1
//List of intermediate files that are part of the cuda dependency
// scanning.
CUDA_ADDITIONAL_CLEAN_FILES:INTERNAL=/home/<USER>/eigenPose/openpose/build/src/openpose/face/CMakeFiles/openpose_face.dir//openpose_face_generated_renderFace.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/gpu/CMakeFiles/openpose_gpu.dir//openpose_gpu_generated_cuda.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/hand/CMakeFiles/openpose_hand.dir//openpose_hand_generated_renderHand.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/net/CMakeFiles/openpose_net.dir//openpose_net_generated_bodyPartConnectorBase.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/net/CMakeFiles/openpose_net.dir//openpose_net_generated_maximumBase.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/net/CMakeFiles/openpose_net.dir//openpose_net_generated_nmsBase.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/net/CMakeFiles/openpose_net.dir//openpose_net_generated_resizeAndMergeBase.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/pose/CMakeFiles/openpose_pose.dir//openpose_pose_generated_renderPose.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/CMakeFiles/openpose.dir/face/openpose_generated_renderFace.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/CMakeFiles/openpose.dir/gpu/openpose_generated_cuda.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/CMakeFiles/openpose.dir/hand/openpose_generated_renderHand.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/CMakeFiles/openpose.dir/net/openpose_generated_bodyPartConnectorBase.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/CMakeFiles/openpose.dir/net/openpose_generated_maximumBase.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/CMakeFiles/openpose.dir/net/openpose_generated_nmsBase.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/CMakeFiles/openpose.dir/net/openpose_generated_resizeAndMergeBase.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/CMakeFiles/openpose.dir/pose/openpose_generated_renderPose.cu.o.depend;/home/<USER>/eigenPose/openpose/build/src/openpose/CMakeFiles/openpose.dir/tracking/openpose_generated_pyramidalLK.cu.o.depend
//STRINGS property for variable: CUDA_ARCH
CUDA_ARCH-STRINGS:INTERNAL=Auto;All;Manual
//ADVANCED property for variable: CUDA_ATTACH_VS_BUILD_RULE_TO_CUDA_FILE
CUDA_ATTACH_VS_BUILD_RULE_TO_CUDA_FILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_BUILD_CUBIN
CUDA_BUILD_CUBIN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_BUILD_EMULATION
CUDA_BUILD_EMULATION-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_CUDART_LIBRARY
CUDA_CUDART_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_CUDA_LIBRARY
CUDA_CUDA_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_GENERATED_OUTPUT_DIR
CUDA_GENERATED_OUTPUT_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_HOST_COMPILATION_CPP
CUDA_HOST_COMPILATION_CPP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_EXECUTABLE
CUDA_NVCC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_FLAGS
CUDA_NVCC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_FLAGS_DEBUG
CUDA_NVCC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_FLAGS_MINSIZEREL
CUDA_NVCC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_FLAGS_RELEASE
CUDA_NVCC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_FLAGS_RELWITHDEBINFO
CUDA_NVCC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_OpenCL_LIBRARY
CUDA_OpenCL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_PROPAGATE_HOST_FLAGS
CUDA_PROPAGATE_HOST_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_SDK_ROOT_DIR
CUDA_SDK_ROOT_DIR-ADVANCED:INTERNAL=1
//This is the value of the last time CUDA_SDK_ROOT_DIR was set
// successfully.
CUDA_SDK_ROOT_DIR_INTERNAL:INTERNAL=CUDA_SDK_ROOT_DIR-NOTFOUND
//ADVANCED property for variable: CUDA_SEPARABLE_COMPILATION
CUDA_SEPARABLE_COMPILATION-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_TOOLKIT_INCLUDE
CUDA_TOOLKIT_INCLUDE-ADVANCED:INTERNAL=1
//This is the value of the last time CUDA_TOOLKIT_ROOT_DIR was
// set successfully.
CUDA_TOOLKIT_ROOT_DIR_INTERNAL:INTERNAL=/usr
//This is the value of the last time CUDA_TOOLKIT_TARGET_DIR was
// set successfully.
CUDA_TOOLKIT_TARGET_DIR_INTERNAL:INTERNAL=/usr
//ADVANCED property for variable: CUDA_VERBOSE_BUILD
CUDA_VERBOSE_BUILD-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_VERSION
CUDA_VERSION-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cublas_LIBRARY
CUDA_cublas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cudadevrt_LIBRARY
CUDA_cudadevrt_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cudart_static_LIBRARY
CUDA_cudart_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufft_LIBRARY
CUDA_cufft_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cupti_LIBRARY
CUDA_cupti_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_curand_LIBRARY
CUDA_curand_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusolver_LIBRARY
CUDA_cusolver_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusparse_LIBRARY
CUDA_cusparse_LIBRARY-ADVANCED:INTERNAL=1
//Returned GPU architectures from op_detect_gpus tool
CUDA_gpu_detect_output:INTERNAL=7.5
//Location of make2cmake.cmake
CUDA_make2cmake:INTERNAL=/usr/share/cmake-3.22/Modules/FindCUDA/make2cmake.cmake
//ADVANCED property for variable: CUDA_nppc_LIBRARY
CUDA_nppc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppial_LIBRARY
CUDA_nppial_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppicc_LIBRARY
CUDA_nppicc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppidei_LIBRARY
CUDA_nppidei_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppif_LIBRARY
CUDA_nppif_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppig_LIBRARY
CUDA_nppig_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppim_LIBRARY
CUDA_nppim_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppist_LIBRARY
CUDA_nppist_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppisu_LIBRARY
CUDA_nppisu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppitc_LIBRARY
CUDA_nppitc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_npps_LIBRARY
CUDA_npps_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvToolsExt_LIBRARY
CUDA_nvToolsExt_LIBRARY-ADVANCED:INTERNAL=1
//Location of parse_cubin.cmake
CUDA_parse_cubin:INTERNAL=/usr/share/cmake-3.22/Modules/FindCUDA/parse_cubin.cmake
//Location of run_nvcc.cmake
CUDA_run_nvcc:INTERNAL=/usr/share/cmake-3.22/Modules/FindCUDA/run_nvcc.cmake
//ADVANCED property for variable: CUDNN_INCLUDE
CUDNN_INCLUDE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDNN_LIBRARY
CUDNN_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDNN_ROOT
CUDNN_ROOT-ADVANCED:INTERNAL=1
//STRINGS property for variable: DL_FRAMEWORK
DL_FRAMEWORK-STRINGS:INTERNAL=CAFFE;NV_CAFFE
//ADVANCED property for variable: DOWNLOAD_SERVER
DOWNLOAD_SERVER-ADVANCED:INTERNAL=1
//Details about finding CUDA
FIND_PACKAGE_MESSAGE_DETAILS_CUDA:INTERNAL=[/usr][/usr/bin/nvcc][/usr/include][/usr/lib/x86_64-linux-gnu/libcudart_static.a][v11.5()]
//Details about finding GFlags
FIND_PACKAGE_MESSAGE_DETAILS_GFlags:INTERNAL=[/usr/include][/usr/lib/x86_64-linux-gnu/libgflags.so][v()]
//Details about finding Glog
FIND_PACKAGE_MESSAGE_DETAILS_Glog:INTERNAL=[/usr/include][/usr/lib/x86_64-linux-gnu/libglog.so][v()]
//Details about finding OpenCV
FIND_PACKAGE_MESSAGE_DETAILS_OpenCV:INTERNAL=[/usr][v4.5.4()]
//Details about finding PYTHON
FIND_PACKAGE_MESSAGE_DETAILS_PYTHON:INTERNAL=/usr/bin/python3.10
//Details about finding Protobuf
FIND_PACKAGE_MESSAGE_DETAILS_Protobuf:INTERNAL=[/usr/lib/x86_64-linux-gnu/libprotobuf.so][/usr/include][v3.12.4()]
//Details about finding PythonInterp
FIND_PACKAGE_MESSAGE_DETAILS_PythonInterp:INTERNAL=[/usr/bin/python3.10][v3.10.12()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: GFLAGS_INCLUDE_DIR
GFLAGS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GFLAGS_LIBRARY
GFLAGS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GFLAGS_ROOT_DIR
GFLAGS_ROOT_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GLOG_INCLUDE_DIR
GLOG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GLOG_LIBRARY
GLOG_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GLOG_ROOT_DIR
GLOG_ROOT_DIR-ADVANCED:INTERNAL=1
//STRINGS property for variable: GPU_MODE
GPU_MODE-STRINGS:INTERNAL=CUDA;OPENCL;CPU_ONLY
//Test HAS_CPP14_FLAG
HAS_CPP14_FLAG:INTERNAL=1
//Test HAS_FLTO
HAS_FLTO:INTERNAL=1
//STRINGS property for variable: INSTRUCTION_SET
INSTRUCTION_SET-STRINGS:INTERNAL=NONE;AVX2
PYBIND11_INCLUDE_DIR:INTERNAL=/home/<USER>/eigenPose/openpose/3rdparty/pybind11/include
PYBIND11_LTO_CXX_FLAGS:INTERNAL=-flto;-fno-fat-lto-objects
PYBIND11_LTO_LINKER_FLAGS:INTERNAL=-flto
PYBIND11_VERSION_MAJOR:INTERNAL=2
PYBIND11_VERSION_MINOR:INTERNAL=3
PYBIND11_VERSION_PATCH:INTERNAL=dev0
//ADVANCED property for variable: PYTHON_EXECUTABLE
PYTHON_EXECUTABLE-ADVANCED:INTERNAL=1
PYTHON_INCLUDE_DIRS:INTERNAL=/usr/include/python3.10
PYTHON_LIBRARIES:INTERNAL=/usr/lib/x86_64-linux-gnu/libpython3.10.so
//ADVANCED property for variable: PYTHON_LIBRARY
PYTHON_LIBRARY-ADVANCED:INTERNAL=1
PYTHON_MODULE_EXTENSION:INTERNAL=.cpython-310-x86_64-linux-gnu.so
PYTHON_MODULE_PREFIX:INTERNAL=
PYTHON_VERSION_MAJOR:INTERNAL=3
PYTHON_VERSION_MINOR:INTERNAL=10
//ADVANCED property for variable: Protobuf_INCLUDE_DIR
Protobuf_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_LIBRARY_DEBUG
Protobuf_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_LIBRARY_RELEASE
Protobuf_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_LITE_LIBRARY_DEBUG
Protobuf_LITE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_LITE_LIBRARY_RELEASE
Protobuf_LITE_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_PROTOC_EXECUTABLE
Protobuf_PROTOC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_PROTOC_LIBRARY_DEBUG
Protobuf_PROTOC_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_PROTOC_LIBRARY_RELEASE
Protobuf_PROTOC_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//STRINGS property for variable: WITH_EIGEN
WITH_EIGEN-STRINGS:INTERNAL=NONE;AUTOBUILD;FIND
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local

