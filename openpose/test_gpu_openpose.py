#!/usr/bin/env python3

import sys
import cv2
import os
import numpy as np

# Add OpenPose Python path
sys.path.append('build/python')
from openpose import pyopenpose as op

def test_openpose_gpu():
    print("Testing OpenPose with GPU acceleration...")
    
    # Parameters
    params = dict()
    params["model_folder"] = "models/"
    params["num_gpu"] = 1
    params["num_gpu_start"] = 0
    
    # Starting OpenPose
    opWrapper = op.WrapperPython()
    opWrapper.configure(params)
    opWrapper.start()
    
    # Test with sample image
    image_path = "examples/media/COCO_val2014_000000000192.jpg"
    if not os.path.exists(image_path):
        print(f"Error: Sample image not found at {image_path}")
        return False
    
    # Process Image
    datum = op.Datum()
    imageToProcess = cv2.imread(image_path)
    datum.cvInputData = imageToProcess
    
    print("Processing image with OpenPose...")
    opWrapper.emplaceAndPop(op.VectorDatum([datum]))
    
    # Check results
    if datum.poseKeypoints is not None:
        num_people = len(datum.poseKeypoints)
        print(f"✅ SUCCESS: Detected {num_people} people in the image")
        print(f"✅ GPU acceleration is working!")
        print(f"✅ Image shape: {imageToProcess.shape}")
        
        # Save output without display
        output_path = "test_gpu_output.jpg"
        if datum.cvOutputData is not None:
            cv2.imwrite(output_path, datum.cvOutputData)
            print(f"✅ Output saved to: {output_path}")
        
        return True
    else:
        print("❌ FAILED: No pose keypoints detected")
        return False

if __name__ == "__main__":
    success = test_openpose_gpu()
    if success:
        print("\n🎉 OpenPose GPU setup is working perfectly!")
        print("You can now use OpenPose for video analysis with GPU acceleration.")
    else:
        print("\n❌ OpenPose GPU setup failed.")
    
    sys.exit(0 if success else 1)
