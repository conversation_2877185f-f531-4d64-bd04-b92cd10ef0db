"""
Utility functions for gait analysis platform.
"""
import numpy as np
import cv2
import json
import math
from typing import List, <PERSON><PERSON>, Dict, Any, Optional
from pathlib import Path
from scipy import signal
from scipy.spatial.distance import euclidean
import logging

logger = logging.getLogger(__name__)

def euclidean_distance(point1: <PERSON><PERSON>[float, float], point2: <PERSON><PERSON>[float, float]) -> float:
    """Calculate Euclidean distance between two 2D points."""
    return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

def calculate_angle(p1: <PERSON><PERSON>[float, float], p2: <PERSON><PERSON>[float, float], p3: <PERSON><PERSON>[float, float]) -> float:
    """
    Calculate angle at point p2 formed by points p1-p2-p3.
    Returns angle in degrees.
    """
    # Vector from p2 to p1
    v1 = (p1[0] - p2[0], p1[1] - p2[1])
    # Vector from p2 to p3
    v2 = (p3[0] - p2[0], p3[1] - p2[1])
    
    # Calculate dot product and magnitudes
    dot_product = v1[0] * v2[0] + v1[1] * v2[1]
    mag_v1 = math.sqrt(v1[0]**2 + v1[1]**2)
    mag_v2 = math.sqrt(v2[0]**2 + v2[1]**2)
    
    # Avoid division by zero
    if mag_v1 == 0 or mag_v2 == 0:
        return 0.0
    
    # Calculate angle in radians then convert to degrees
    cos_angle = dot_product / (mag_v1 * mag_v2)
    cos_angle = max(-1.0, min(1.0, cos_angle))  # Clamp to valid range
    angle_rad = math.acos(cos_angle)
    return math.degrees(angle_rad)

def smooth_keypoints(keypoints_sequence: List[np.ndarray], window_size: int = 5) -> List[np.ndarray]:
    """
    Smooth keypoint sequences using a moving average filter.
    
    Args:
        keypoints_sequence: List of keypoint arrays for each frame
        window_size: Size of smoothing window
        
    Returns:
        Smoothed keypoint sequences
    """
    if len(keypoints_sequence) < window_size:
        return keypoints_sequence
    
    smoothed = []
    for i in range(len(keypoints_sequence)):
        start_idx = max(0, i - window_size // 2)
        end_idx = min(len(keypoints_sequence), i + window_size // 2 + 1)
        
        # Average keypoints in the window
        window_keypoints = keypoints_sequence[start_idx:end_idx]
        if window_keypoints:
            avg_keypoints = np.mean(window_keypoints, axis=0)
            smoothed.append(avg_keypoints)
        else:
            smoothed.append(keypoints_sequence[i])
    
    return smoothed

def filter_low_confidence_keypoints(keypoints: np.ndarray, confidence_threshold: float = 0.3) -> np.ndarray:
    """
    Set low confidence keypoints to NaN.
    
    Args:
        keypoints: Array of shape (num_people, num_keypoints, 3) where last dim is [x, y, confidence]
        confidence_threshold: Minimum confidence threshold
        
    Returns:
        Filtered keypoints array
    """
    filtered = keypoints.copy()
    if len(filtered.shape) == 3:  # Multiple people
        for person_idx in range(filtered.shape[0]):
            for kp_idx in range(filtered.shape[1]):
                if filtered[person_idx, kp_idx, 2] < confidence_threshold:
                    filtered[person_idx, kp_idx, :2] = np.nan
    elif len(filtered.shape) == 2:  # Single person
        for kp_idx in range(filtered.shape[0]):
            if filtered[kp_idx, 2] < confidence_threshold:
                filtered[kp_idx, :2] = np.nan
    
    return filtered

def estimate_body_height(keypoints: np.ndarray, method: str = "hip_to_ankle") -> float:
    """
    Estimate body height from keypoints.
    
    Args:
        keypoints: Keypoint array for a single person
        method: Method for height estimation
        
    Returns:
        Estimated height in pixels
    """
    if method == "hip_to_ankle":
        # Use average of left and right hip to ankle distances
        left_hip = keypoints[12, :2]  # LHip
        right_hip = keypoints[9, :2]  # RHip
        left_ankle = keypoints[14, :2]  # LAnkle
        right_ankle = keypoints[11, :2]  # RAnkle
        
        distances = []
        if not (np.isnan(left_hip).any() or np.isnan(left_ankle).any()):
            distances.append(euclidean_distance(left_hip, left_ankle))
        if not (np.isnan(right_hip).any() or np.isnan(right_ankle).any()):
            distances.append(euclidean_distance(right_hip, right_ankle))
        
        return np.mean(distances) if distances else 100.0  # Default fallback
    
    elif method == "full_body":
        # Use neck to ankle distance
        neck = keypoints[1, :2]  # Neck
        left_ankle = keypoints[14, :2]  # LAnkle
        right_ankle = keypoints[11, :2]  # RAnkle
        
        distances = []
        if not (np.isnan(neck).any() or np.isnan(left_ankle).any()):
            distances.append(euclidean_distance(neck, left_ankle))
        if not (np.isnan(neck).any() or np.isnan(right_ankle).any()):
            distances.append(euclidean_distance(neck, right_ankle))
        
        return np.mean(distances) if distances else 150.0  # Default fallback
    
    return 100.0  # Default fallback

def normalize_by_body_height(measurement: float, body_height: float) -> float:
    """Normalize measurement by body height."""
    if body_height == 0:
        return 0.0
    return measurement / body_height

def apply_butterworth_filter(data: np.ndarray, cutoff_freq: float = 6.0, 
                           sampling_rate: float = 30.0, order: int = 2) -> np.ndarray:
    """
    Apply Butterworth low-pass filter to data.
    
    Args:
        data: Input data array
        cutoff_freq: Cutoff frequency in Hz
        sampling_rate: Sampling rate in Hz
        order: Filter order
        
    Returns:
        Filtered data
    """
    if len(data) < 4:  # Need minimum data points for filtering
        return data
    
    nyquist = sampling_rate / 2
    normalized_cutoff = cutoff_freq / nyquist
    
    # Ensure cutoff frequency is valid
    if normalized_cutoff >= 1.0:
        return data
    
    try:
        b, a = signal.butter(order, normalized_cutoff, btype='low')
        filtered_data = signal.filtfilt(b, a, data)
        return filtered_data
    except Exception as e:
        logger.warning(f"Filter application failed: {e}")
        return data

def save_json_results(results: Dict[str, Any], output_path: Path) -> None:
    """Save analysis results to JSON file."""
    try:
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        logger.info(f"Results saved to {output_path}")
    except Exception as e:
        logger.error(f"Failed to save results: {e}")

def load_json_keypoints(json_path: Path) -> Dict[str, Any]:
    """Load keypoints from OpenPose JSON output."""
    try:
        with open(json_path, 'r') as f:
            data = json.load(f)
        return data
    except Exception as e:
        logger.error(f"Failed to load JSON keypoints: {e}")
        return {}

def validate_video_file(video_path: Path, supported_formats: List[str]) -> bool:
    """Validate if video file is supported and accessible."""
    if not video_path.exists():
        logger.error(f"Video file not found: {video_path}")
        return False
    
    if video_path.suffix.lower() not in supported_formats:
        logger.error(f"Unsupported video format: {video_path.suffix}")
        return False
    
    # Try to open with OpenCV
    try:
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            logger.error(f"Cannot open video file: {video_path}")
            return False
        cap.release()
        return True
    except Exception as e:
        logger.error(f"Error validating video file: {e}")
        return False

def get_video_properties(video_path: Path) -> Dict[str, Any]:
    """Get video properties like FPS, duration, resolution."""
    try:
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return {}
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            "fps": fps,
            "frame_count": frame_count,
            "width": width,
            "height": height,
            "duration": duration,
        }
    except Exception as e:
        logger.error(f"Error getting video properties: {e}")
        return {}

def create_output_directory(base_dir: Path, video_name: str) -> Path:
    """Create output directory for video analysis results."""
    output_dir = base_dir / f"{video_name}_analysis"
    output_dir.mkdir(exist_ok=True)
    return output_dir
