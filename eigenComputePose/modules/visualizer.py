"""
Gait visualization module for creating annotated images and videos.
"""
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging
import json

logger = logging.getLogger(__name__)

class GaitVisualizer:
    """
    Visualizer for creating gait analysis visualizations.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize gait visualizer.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.keypoint_indices = config.get("keypoints", {})

        # Colors for different event types
        self.event_colors = {
            "initial_contact": (0, 255, 0),  # Green
            "toe_off": (0, 0, 255),          # Red
            "midstance": (255, 255, 0),      # Yellow
            "swing": (255, 0, 255),          # Magenta
        }

        # Text properties
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.7
        self.font_thickness = 2
        self.text_color = (255, 255, 255)  # White
        self.text_bg_color = (0, 0, 0)     # Black background

    def create_visualizations(self, video_path: Path, output_dir: Path,
                            pose_results: Dict[str, Any], gait_data: Dict[str, Any],
                            keypoints_sequence: List[np.ndarray], fps: float) -> List[str]:
        """
        Create all visualization outputs.

        Args:
            video_path: Original video path
            output_dir: Output directory
            pose_results: OpenPose processing results
            gait_data: Gait cycle detection results
            keypoints_sequence: Keypoint sequences
            fps: Video frame rate

        Returns:
            List of created visualization file paths
        """
        logger.info("Creating gait analysis visualizations...")

        visualization_files = []

        try:
            # 1. Create gait event images
            event_images = self._create_gait_event_images(
                output_dir, gait_data, fps
            )
            visualization_files.extend(event_images)

            # 2. Create annotated video with gait events
            annotated_video = self._create_annotated_video(
                video_path, output_dir, gait_data, fps
            )
            if annotated_video:
                visualization_files.append(annotated_video)

            # 2b. Create video from visualization frames if pose video doesn't exist
            if not annotated_video:
                frames_video = self._create_video_from_frames(output_dir, fps)
                if frames_video:
                    visualization_files.append(frames_video)

            # 3. Create gait cycle summary images
            summary_images = self._create_gait_cycle_summary(
                output_dir, gait_data, keypoints_sequence, fps
            )
            visualization_files.extend(summary_images)

            logger.info(f"Created {len(visualization_files)} visualization files")

        except Exception as e:
            logger.error(f"Error creating visualizations: {e}")

        return visualization_files

    def _create_gait_event_images(self, output_dir: Path, gait_data: Dict[str, Any], fps: float) -> List[str]:
        """Create images for specific gait events."""
        event_images = []

        # Create directory for event images
        events_dir = output_dir / "gait_event_images"
        events_dir.mkdir(exist_ok=True)

        # Get visualization frames directory
        viz_frames_dir = output_dir / "visualization_frames"

        if not viz_frames_dir.exists():
            logger.warning("Visualization frames not found, skipping event images")
            return event_images

        # Process each gait event
        for event in gait_data.get("events", []):
            try:
                frame_idx = event["frame"]
                event_type = event["event_type"]
                foot = event["foot"]
                timestamp = event["timestamp"]

                # Find corresponding visualization frame
                viz_frame_path = viz_frames_dir / f"frame_{frame_idx:06d}_pose.jpg"

                if viz_frame_path.exists():
                    # Load the visualization frame
                    frame = cv2.imread(str(viz_frame_path))

                    if frame is not None:
                        # Add event annotation
                        annotated_frame = self._annotate_gait_event(
                            frame, event_type, foot, timestamp, frame_idx
                        )

                        # Save annotated event image
                        event_filename = f"{event_type}_{foot}_frame_{frame_idx:06d}.jpg"
                        event_image_path = events_dir / event_filename
                        cv2.imwrite(str(event_image_path), annotated_frame)

                        event_images.append(str(event_image_path))
                        logger.debug(f"Created event image: {event_filename}")

            except Exception as e:
                logger.error(f"Error creating event image for frame {frame_idx}: {e}")

        return event_images

    def _annotate_gait_event(self, frame: np.ndarray, event_type: str, foot: str,
                           timestamp: float, frame_idx: int) -> np.ndarray:
        """Add gait event annotation to frame."""
        annotated_frame = frame.copy()

        # Get event color
        color = self.event_colors.get(event_type, (255, 255, 255))

        # Create annotation text
        event_text = f"{event_type.replace('_', ' ').title()}"
        foot_text = f"{foot.title()} Foot"
        time_text = f"Time: {timestamp:.2f}s"
        frame_text = f"Frame: {frame_idx}"

        # Position for text (top-left corner)
        y_offset = 30
        texts = [event_text, foot_text, time_text, frame_text]

        for i, text in enumerate(texts):
            y_pos = y_offset + (i * 35)

            # Get text size for background rectangle
            (text_width, text_height), baseline = cv2.getTextSize(
                text, self.font, self.font_scale, self.font_thickness
            )

            # Draw background rectangle
            cv2.rectangle(
                annotated_frame,
                (10, y_pos - text_height - 5),
                (10 + text_width + 10, y_pos + baseline + 5),
                self.text_bg_color,
                -1
            )

            # Draw text
            cv2.putText(
                annotated_frame,
                text,
                (15, y_pos),
                self.font,
                self.font_scale,
                color if i == 0 else self.text_color,
                self.font_thickness
            )

        # Add event indicator circle
        center_x = frame.shape[1] - 50
        center_y = 50
        cv2.circle(annotated_frame, (center_x, center_y), 20, color, -1)
        cv2.circle(annotated_frame, (center_x, center_y), 20, (255, 255, 255), 2)

        return annotated_frame

    def _create_annotated_video(self, video_path: Path, output_dir: Path,
                              gait_data: Dict[str, Any], fps: float) -> str:
        """Create annotated video with gait events overlay."""
        try:
            # Check if pose visualization video exists
            pose_video_path = output_dir / "pose_visualization.mp4"
            if not pose_video_path.exists():
                logger.warning("Pose visualization video not found, skipping annotated video")
                return ""

            # Output path for annotated video
            annotated_video_path = output_dir / "gait_analysis_annotated.mp4"

            # Open input video
            cap = cv2.VideoCapture(str(pose_video_path))
            if not cap.isOpened():
                logger.error(f"Cannot open pose visualization video: {pose_video_path}")
                return ""

            # Get video properties
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # Setup video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(annotated_video_path), fourcc, fps, (width, height))

            # Create event lookup for fast access
            events_by_frame = {}
            for event in gait_data.get("events", []):
                frame_idx = event["frame"]
                if frame_idx not in events_by_frame:
                    events_by_frame[frame_idx] = []
                events_by_frame[frame_idx].append(event)

            # Process each frame
            frame_idx = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # Add gait event annotations if present
                if frame_idx in events_by_frame:
                    for event in events_by_frame[frame_idx]:
                        frame = self._add_event_overlay(frame, event)

                # Add frame counter and timestamp
                frame = self._add_frame_info(frame, frame_idx, fps)

                # Write frame
                out.write(frame)
                frame_idx += 1

            # Cleanup
            cap.release()
            out.release()

            logger.info(f"Created annotated video: {annotated_video_path}")
            return str(annotated_video_path)

        except Exception as e:
            logger.error(f"Error creating annotated video: {e}")
            return ""

    def _add_event_overlay(self, frame: np.ndarray, event: Dict[str, Any]) -> np.ndarray:
        """Add event overlay to frame."""
        event_type = event["event_type"]
        foot = event["foot"]
        color = self.event_colors.get(event_type, (255, 255, 255))

        # Add event indicator at bottom of frame
        text = f"{event_type.replace('_', ' ').title()} - {foot.title()}"

        # Position at bottom center
        (text_width, text_height), baseline = cv2.getTextSize(
            text, self.font, self.font_scale, self.font_thickness
        )

        x_pos = (frame.shape[1] - text_width) // 2
        y_pos = frame.shape[0] - 30

        # Background rectangle
        cv2.rectangle(
            frame,
            (x_pos - 10, y_pos - text_height - 5),
            (x_pos + text_width + 10, y_pos + baseline + 5),
            self.text_bg_color,
            -1
        )

        # Text
        cv2.putText(
            frame,
            text,
            (x_pos, y_pos),
            self.font,
            self.font_scale,
            color,
            self.font_thickness
        )

        return frame

    def _add_frame_info(self, frame: np.ndarray, frame_idx: int, fps: float) -> np.ndarray:
        """Add frame number and timestamp to frame."""
        timestamp = frame_idx / fps if fps > 0 else 0
        info_text = f"Frame: {frame_idx} | Time: {timestamp:.2f}s"

        # Position at top right
        (text_width, text_height), baseline = cv2.getTextSize(
            info_text, self.font, 0.5, 1
        )

        x_pos = frame.shape[1] - text_width - 10
        y_pos = 25

        # Background rectangle
        cv2.rectangle(
            frame,
            (x_pos - 5, y_pos - text_height - 2),
            (x_pos + text_width + 5, y_pos + baseline + 2),
            self.text_bg_color,
            -1
        )

        # Text
        cv2.putText(
            frame,
            info_text,
            (x_pos, y_pos),
            self.font,
            0.5,
            self.text_color,
            1
        )

        return frame

    def _create_gait_cycle_summary(self, output_dir: Path, gait_data: Dict[str, Any],
                                 keypoints_sequence: List[np.ndarray], fps: float) -> List[str]:
        """Create summary images for gait cycles."""
        summary_images = []

        # Create directory for summary images
        summary_dir = output_dir / "gait_cycle_summaries"
        summary_dir.mkdir(exist_ok=True)

        # Create summary for each gait cycle
        for i, cycle in enumerate(gait_data.get("cycles", [])):
            try:
                summary_path = self._create_cycle_summary_image(
                    summary_dir, cycle, i, keypoints_sequence, fps
                )
                if summary_path:
                    summary_images.append(summary_path)

            except Exception as e:
                logger.error(f"Error creating summary for cycle {i}: {e}")

        return summary_images

    def _create_cycle_summary_image(self, summary_dir: Path, cycle: Dict[str, Any],
                                  cycle_idx: int, keypoints_sequence: List[np.ndarray],
                                  fps: float) -> str:
        """Create summary image for a single gait cycle."""
        try:
            # Create a blank image for the summary
            img_width = 800
            img_height = 600
            summary_img = np.zeros((img_height, img_width, 3), dtype=np.uint8)

            # Add cycle information
            foot = cycle["foot"]
            start_frame = cycle["start_frame"]
            end_frame = cycle["end_frame"]
            duration = cycle["duration_seconds"]

            # Title
            title = f"Gait Cycle {cycle_idx + 1} - {foot.title()} Foot"
            cv2.putText(summary_img, title, (20, 40), self.font, 1.0, (255, 255, 255), 2)

            # Cycle details
            details = [
                f"Duration: {duration:.2f} seconds",
                f"Frames: {start_frame} - {end_frame}",
                f"Frame Count: {end_frame - start_frame}",
                f"Frequency: {1/duration:.1f} Hz" if duration > 0 else "Frequency: N/A"
            ]

            for i, detail in enumerate(details):
                y_pos = 80 + (i * 30)
                cv2.putText(summary_img, detail, (20, y_pos), self.font, 0.6, (200, 200, 200), 1)

            # Save summary image
            summary_filename = f"cycle_{cycle_idx + 1}_{foot}_summary.jpg"
            summary_path = summary_dir / summary_filename
            cv2.imwrite(str(summary_path), summary_img)

            logger.debug(f"Created cycle summary: {summary_filename}")
            return str(summary_path)

        except Exception as e:
            logger.error(f"Error creating cycle summary image: {e}")
            return ""

    def _create_video_from_frames(self, output_dir: Path, fps: float) -> str:
        """Create video from visualization frames."""
        try:
            viz_frames_dir = output_dir / "visualization_frames"
            if not viz_frames_dir.exists():
                logger.warning("Visualization frames directory not found")
                return ""

            # Get all frame files
            frame_files = sorted(list(viz_frames_dir.glob("frame_*_pose.jpg")))
            if not frame_files:
                logger.warning("No visualization frames found")
                return ""

            # Read first frame to get dimensions
            first_frame = cv2.imread(str(frame_files[0]))
            if first_frame is None:
                logger.error("Could not read first frame")
                return ""

            height, width = first_frame.shape[:2]

            # Output video path
            output_video_path = output_dir / "pose_visualization_from_frames.mp4"

            # Setup video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_video_path), fourcc, fps, (width, height))

            if not out.isOpened():
                logger.error("Failed to initialize video writer for frames video")
                return ""

            # Write all frames
            for frame_file in frame_files:
                frame = cv2.imread(str(frame_file))
                if frame is not None:
                    out.write(frame)

            out.release()

            logger.info(f"Created video from frames: {output_video_path}")
            return str(output_video_path)

        except Exception as e:
            logger.error(f"Error creating video from frames: {e}")
            return ""
