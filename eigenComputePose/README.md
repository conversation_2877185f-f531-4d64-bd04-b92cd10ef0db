# Video Gait Analysis Platform

A comprehensive video analysis platform for running gait analysis using OpenPose. This platform processes videos from an input directory and extracts 14 specific biomechanical measurements from running gait cycles.

## 🎯 Features

- **OpenPose Integration**: Uses OpenPose Python API with GPU acceleration
- **Gait Cycle Detection**: Automatic detection of swing phase → initial contact → midstance → toe off → swing phase
- **14 Biomechanical Measurements**: Comprehensive analysis including pelvic drop, pronation, knee drift, and more
- **Modular Architecture**: Separate modules for video processing, gait detection, and biomechanical analysis
- **FastAPI Endpoints**: RESTful API for integration and deployment
- **Docker Support**: Ready for containerized deployment
- **Batch Processing**: Process multiple videos automatically

## 📊 Biomechanical Measurements

1. **pelvicDrop** - Pelvic drop direction analysis
2. **Pronation** - Ankle pronation/supination angle between knee and heel
3. **kneeDrift** - Knee valgus/varus at midstance (ratio of inter-knee to inter-hip distances)
4. **footDrift** - Foot crossover gait detection
5. **heelWhip** - Medial/lateral heel movement relative to midline
6. **posteriorStabilityScore** - Composite score (0.4×footDrift + 0.3×kneeDrift + 0.2×heelWhip + 0.1×pelvicDrop)
7. **overstride** - Normalized heel-to-hip distance at initial contact
8. **ankleDorsiflexion** - Foot strike type classification
9. **anklePlantarflexion** - Propulsive power and push-off mechanics
10. **verticalOscillationVideo** - Vertical oscillation in centimeters
11. **trunkLean** - Trunk lean angle in degrees
12. **Ground_contact_Time** - IC to TO duration in milliseconds
13. **Cadence** - Steps per second
14. **kneeFlexionLoading** - Knee flexion angle during loading phase

## 🚀 Quick Start

### Prerequisites

- Ubuntu 20.04/22.04
- NVIDIA GPU with CUDA 11.5+
- OpenPose installed (already available in this setup)
- Python 3.8+

### Installation

1. **Install Dependencies**:
```bash
cd eigenComputePose
pip install -r requirements.txt
```

2. **Verify OpenPose Setup**:
```bash
# Check if OpenPose is working
cd ../openpose
python3 test_gpu_openpose.py
```

3. **Test the Platform**:
```bash
cd ../eigenComputePose
python main.py --help
```

### Basic Usage

#### Command Line Interface

```bash
# Process a single video
python main.py --video inputVideos/running_video.mp4

# Process all videos in input directory
python main.py --batch

# Process with custom directories
python main.py --batch --input-dir /path/to/videos --output-dir /path/to/results

# Process with debug logging
python main.py --video test.mp4 --log-level DEBUG
```

#### API Usage

1. **Start the API server**:
```bash
python -m uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
```

2. **Access the API documentation**:
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

3. **Example API calls**:
```bash
# Health check
curl http://localhost:8000/health

# List available videos
curl http://localhost:8000/videos

# Analyze a video
curl -X POST "http://localhost:8000/analyze" \
     -H "Content-Type: application/json" \
     -d '{"video_filename": "running_video.mp4"}'

# Batch analysis
curl -X POST "http://localhost:8000/analyze/batch" \
     -H "Content-Type: application/json" \
     -d '{}'
```

## 📁 Project Structure

```
eigenComputePose/
├── main.py                 # Main orchestration script
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Docker orchestration
├── README.md              # This file
├── inputVideos/           # Input video directory
├── outputResults/         # Analysis results
├── modules/               # Analysis modules
│   ├── __init__.py
│   ├── video_processor.py      # OpenPose video processing
│   ├── gait_cycle_detector.py  # Gait cycle detection
│   ├── biomechanical_analyzer.py # 14 measurements
│   └── utils.py               # Helper functions
├── api/                   # FastAPI endpoints
│   ├── __init__.py
│   ├── main.py           # FastAPI app
│   └── models.py         # Pydantic models
└── tests/                # Unit tests
    ├── __init__.py
    └── test_basic.py
```

## 🔧 Configuration

The platform uses a comprehensive configuration system in `config.py`. Key settings include:

- **OpenPose Settings**: Model paths, GPU configuration, network resolution
- **Video Processing**: Supported formats, FPS thresholds, resize settings
- **Gait Analysis**: Confidence thresholds, smoothing parameters, cycle detection
- **Measurements**: Body height estimation, normalization methods, filtering

## 🐳 Docker Deployment

### Build and Run

```bash
# Build the Docker image
docker build -t gait-analysis-platform .

# Run with Docker Compose
docker-compose up -d

# Check logs
docker-compose logs -f gait-analysis
```

### Environment Variables

```bash
# Set in docker-compose.yml or .env file
NVIDIA_VISIBLE_DEVICES=all
NVIDIA_DRIVER_CAPABILITIES=compute,utility
```

## 🧪 Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov

# Run tests
python -m pytest tests/ -v

# Run with coverage
python -m pytest tests/ --cov=modules --cov-report=html
```

## 📈 Development Workflow

### Adding New Measurements

1. **Add measurement function** to `BiomechanicalAnalyzer` class
2. **Update the main analysis method** to call your function
3. **Add tests** in `tests/test_biomechanical.py`
4. **Update API models** if needed

### Example: Adding a new measurement

```python
def calculate_new_measurement(self, keypoints_sequence, gait_data):
    """Calculate a new biomechanical measurement."""
    try:
        # Your calculation logic here
        result = 0.0
        return float(result)
    except Exception as e:
        logger.error(f"Error calculating new measurement: {e}")
        return 0.0
```

## 🔍 Gait Cycle Detection

The platform uses multiple methods for robust gait cycle detection:

### Initial Contact Detection
1. **Primary Method**: Biggest vertical deceleration of large toe
2. **Secondary Method**: Foot velocity near zero while on ground

### Toe-Off Detection
- Sudden upward movement of toe
- Velocity-based detection

### Validation
- Minimum/maximum cycle duration constraints
- Confidence-based event filtering
- Temporal consistency checks

## 📊 Output Format

### Analysis Results Structure

```json
{
  "video_info": {
    "path": "inputVideos/running_video.mp4",
    "name": "running_video",
    "properties": {
      "fps": 30.0,
      "duration": 10.5,
      "width": 1920,
      "height": 1080
    }
  },
  "gait_analysis": {
    "cycles": [...],
    "phases": [...],
    "events": [...],
    "total_cycles": 5
  },
  "biomechanical_measurements": {
    "pelvicDrop": 2.5,
    "Pronation": 15.2,
    "kneeDrift": 0.95,
    "footDrift": 8.1,
    "heelWhip": 12.3,
    "posteriorStabilityScore": 7.8,
    "overstride": 0.15,
    "ankleDorsiflexion": 85.2,
    "anklePlantarflexion": 110.5,
    "verticalOscillationVideo": 8.5,
    "trunkLean": 5.2,
    "Ground_contact_Time": 180.5,
    "Cadence": 2.8,
    "kneeFlexionLoading": 25.8
  }
}
```

## 🚨 Troubleshooting

### Common Issues

1. **OpenPose Import Error**:
   ```bash
   # Check Python path
   export PYTHONPATH=/home/<USER>/eigenPose/openpose/build/python:$PYTHONPATH
   ```

2. **GPU Memory Issues**:
   - Reduce video resolution in config
   - Process videos in smaller batches
   - Check CUDA memory usage: `nvidia-smi`

3. **No Gait Cycles Detected**:
   - Check video quality and frame rate
   - Verify person is visible throughout video
   - Adjust confidence thresholds in config

4. **API Server Issues**:
   ```bash
   # Check if port is available
   netstat -tulpn | grep 8000
   
   # Run with debug mode
   uvicorn api.main:app --reload --log-level debug
   ```

## 📝 License

This project is part of the eigenPose platform for biomechanical analysis.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review the API documentation at `/docs`
- Check OpenPose setup in `../SETUP_COMPLETE.md`
