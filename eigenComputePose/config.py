"""
Configuration settings for the video analysis platform.
"""
import os
from pathlib import Path
from typing import Dict, Any

# Base paths
BASE_DIR = Path(__file__).parent
OPENPOSE_ROOT = BASE_DIR.parent / "openpose"
OPENPOSE_PYTHON_PATH = OPENPOSE_ROOT / "build" / "python"
OPENPOSE_MODELS_PATH = OPENPOSE_ROOT / "models"

# Input/Output directories
INPUT_VIDEOS_DIR = BASE_DIR / "inputVideos"
OUTPUT_RESULTS_DIR = BASE_DIR / "outputResults"
TEMP_DIR = BASE_DIR / "temp"

# Create directories if they don't exist
for directory in [INPUT_VIDEOS_DIR, OUTPUT_RESULTS_DIR, TEMP_DIR]:
    directory.mkdir(exist_ok=True)

# OpenPose configuration
OPENPOSE_CONFIG = {
    "model_folder": str(OPENPOSE_MODELS_PATH),
    "num_gpu": 1,
    "num_gpu_start": 0,
    "model_pose": "BODY_25",
    "net_resolution": "656x368",  # Good balance of speed and accuracy
    "output_resolution": "1280x720",
    "scale_number": 1,
    "scale_gap": 0.3,
    "keypoint_scale": 0,  # 0=scale to image, 1=scale to net, 2=relative to threshold
    "disable_blending": False,
    "render_threshold": 0.05,
    "alpha_pose": 0.6,
    "alpha_heatmap": 0.7,
    "part_to_show": 0,
    "display": 0,  # No display for headless operation
    "write_json": "",  # Will be set dynamically
    "write_coco_json": "",
    "write_images": "",
    "write_video": "",
}

# Video processing settings
VIDEO_CONFIG = {
    "supported_formats": [".mp4", ".avi", ".mov", ".mkv", ".wmv"],
    "fps_threshold": 24,  # Minimum FPS for analysis
    "min_duration": 2.0,  # Minimum video duration in seconds
    "max_duration": 300.0,  # Maximum video duration in seconds
    "resize_height": 720,  # Resize videos to this height for processing
}

# Gait analysis settings
GAIT_CONFIG = {
    "min_confidence": 0.3,  # Minimum keypoint confidence
    "smoothing_window": 5,  # Frames for smoothing
    "gait_cycle_min_frames": 20,  # Minimum frames for a gait cycle
    "gait_cycle_max_frames": 120,  # Maximum frames for a gait cycle
    "velocity_threshold": 0.1,  # Threshold for detecting foot contact
    "height_threshold": 0.02,  # Relative height threshold for foot contact
}

# Biomechanical measurement settings
MEASUREMENT_CONFIG = {
    "body_height_estimation": "hip_to_ankle",  # Method for estimating body height
    "normalization_method": "body_height",  # Normalization approach
    "angle_calculation_method": "vector",  # Method for angle calculations
    "filter_type": "butterworth",  # Filter type for smoothing
    "filter_order": 2,  # Filter order
    "filter_cutoff": 6.0,  # Cutoff frequency in Hz
}

# BODY_25 keypoint indices
BODY25_KEYPOINTS = {
    "Nose": 0,
    "Neck": 1,
    "RShoulder": 2,
    "RElbow": 3,
    "RWrist": 4,
    "LShoulder": 5,
    "LElbow": 6,
    "LWrist": 7,
    "MidHip": 8,
    "RHip": 9,
    "RKnee": 10,
    "RAnkle": 11,
    "LHip": 12,
    "LKnee": 13,
    "LAnkle": 14,
    "REye": 15,
    "LEye": 16,
    "REar": 17,
    "LEar": 18,
    "LBigToe": 19,
    "LSmallToe": 20,
    "LHeel": 21,
    "RBigToe": 22,
    "RSmallToe": 23,
    "RHeel": 24,
}

# Logging configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
    "rotation": "10 MB",
    "retention": "1 week",
    "log_file": str(BASE_DIR / "logs" / "gait_analysis.log"),
}

# API configuration
API_CONFIG = {
    "host": "0.0.0.0",
    "port": 8000,
    "reload": False,
    "workers": 1,
    "title": "Gait Analysis API",
    "description": "Video analysis platform for running gait analysis using OpenPose",
    "version": "1.0.0",
}

# Docker configuration
DOCKER_CONFIG = {
    "base_image": "nvidia/cuda:11.5-cudnn8-devel-ubuntu20.04",
    "python_version": "3.10",
    "working_dir": "/app",
    "expose_port": 8000,
}

def get_config() -> Dict[str, Any]:
    """Get complete configuration dictionary."""
    return {
        "openpose": OPENPOSE_CONFIG,
        "video": VIDEO_CONFIG,
        "gait": GAIT_CONFIG,
        "measurement": MEASUREMENT_CONFIG,
        "keypoints": BODY25_KEYPOINTS,
        "logging": LOGGING_CONFIG,
        "api": API_CONFIG,
        "docker": DOCKER_CONFIG,
        "paths": {
            "base_dir": str(BASE_DIR),
            "openpose_root": str(OPENPOSE_ROOT),
            "openpose_python": str(OPENPOSE_PYTHON_PATH),
            "openpose_models": str(OPENPOSE_MODELS_PATH),
            "input_videos": str(INPUT_VIDEOS_DIR),
            "output_results": str(OUTPUT_RESULTS_DIR),
            "temp_dir": str(TEMP_DIR),
        }
    }
